<template>
  <el-dialog
    v-model="dlgProp.show"
    :title="dlgProp.title"
    :destroy-on-close="true"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    draggable
  >
    <el-form
      ref="formRef"
      :model="unionForm"
      status-icon
      :hide-required-asterisk="false"
      label-width="120px"
      style="margin-top: 20px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="名称" prop="name">
            <el-input v-model="unionForm.name" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="描述" prop="showname" >
            <el-input v-model="unionForm.showname" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主表" prop="mainid">
            <el-select v-model="unionForm.mainid" @change="getMainColumns(unionForm.mainid,maincolumnList)" :disabled="dlgProp.type==0">
              <el-option v-for="(t) in tableList" :key="t.value" :label="t.label" :value="t.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主表字段" prop="maincolumnid">
            <el-select v-model="unionForm.maincolumnid" >
              <el-option v-for="(t) in maincolumnList" :key="t.id" :label="t.showname + ' / ' + t.name" :value="t.id+''"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联类型" prop="type" >
            <el-radio-group v-model="unionForm.type" >
              <el-radio-button v-for="(t) in unionsqltypes" :label="t.label" :value="t.value" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" >
          <el-form-item label="关联表" prop="unionmainid" >
            <el-select v-model="unionForm.unionmainid"  @change="getMainColumns(unionForm.unionmainid,unioncolumnList)" :disabled="dlgProp.type==1">
              <el-option v-for="(t) in tableList" :key="t.value" :label="t.label" :value="t.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联字段" prop="unioncolumnid" >
            <el-select v-model="unionForm.unioncolumnid" >
              <el-option v-for="(t) in unioncolumnList" :key="t.id" :label="t.showname + ' / ' + t.name" :value="t.id+''"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联显示字段" prop="unionshowid" >
            <el-select v-model="unionForm.unionshowid" >
              <el-option v-for="(t) in unioncolumnList" :key="t.id" :label="t.showname + ' / ' + t.name" :value="t.id+''"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="子表显示位置" prop="showpos"  >
            <el-select v-model="unionForm.showpos" multiple>
              <el-option v-for="(t) in showposlist" :label="t.label" :value="t.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联方式" prop="uniontype"  >
            <el-radio-group v-model="unionForm.uniontype" >
              <el-radio-button v-for="(t) in uniontypes" :label="t.label" :value="t.value" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="orderseq" >
            <el-input-number v-model="unionForm.orderseq" :min="1" :max="10"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="unionForm.uniontype==1">
          <el-form-item label="关联SQL" prop="unionsql" >
            <el-input type="textarea" v-model="unionForm.unionsql"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="dlgProp.show = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import {watch, ref, reactive} from 'vue'
import {getUnionedList, getUnionList, saveUnion} from "../../../api/dev/union";
import {getColumnList} from "../../../api/dev/column";
import {getAllMainByType} from "../../../api/dev/main";
import {ElMessage} from "element-plus";

const unionsqltypes = [
  {
    label: '左关联(LEFT JOIN)',
    value: ' left join '
  },
  {
    label: '内关联(INNER JOIN)',
    value: ' inner join '
  },
]

const uniontypes = [
  {
    label: '字段关联',
    value: 0
  },
  {
    label: 'SQL关联',
    value: 1
  },
]

const showposlist = [
  {
    label: '表格顶部按钮',
    value: 'topbtn'
  },
  {
    label: '表格行按钮',
    value: 'rowbtn'
  },
  {
    label: '修改页的标签页',
    value: 'edittab'
  },
  {
    label: '修改页底部',
    value: 'editbottom'
  },
  {
    label: '详情页的标签页',
    value: 'detailtab'
  },
  {
    label: '详情页底部',
    value: 'detailbottom'
  },
]


const emit = defineEmits(['save'])


const save = async () => {
  let obj = {};
  Object.assign(obj,unionForm.value);

  obj.showpos =  JSON.stringify(obj.showpos);

  const resp = await saveUnion(obj) ;

  if(resp.code == 200) {
    ElMessage.success({ message: resp.msg });

    emit('save',dlgProp.value.type);
    dlgProp.value.show = false;
  }else {
    ElMessage.error({ message: resp.msg });
  }

}


const tableList = reactive([]);
const maincolumnList = reactive([]);
const unioncolumnList = reactive([]);





const getMainColumns = async (mid,list) => {
  if(!mid) return;
  const { data } = await getColumnList(mid);
  Object.assign(list,data);
  console.log(list);
}

const getTableList = async () => {
  const maintype = dlgProp.value.maintype;
  if(!maintype) return;
  const { data } = await getAllMainByType(maintype);
  Object.assign(tableList,data);
}


const unionForm = ref();

const dlgProp = ref({
  show: false,
  title: '新增关联表',
  maintype: 1,
  type: 0,   //  0 关联表  1 被关联表
})



// 接收父组件参数
const openDialog = (params) => {
  dlgProp.value = params.dlgProp;
  unionForm.value = params.obj;
  if(unionForm.value.orderseq) unionForm.value.orderseq = Number(unionForm.value.orderseq)
  dlgProp.value.show = true;
  getTableList();

  if(unionForm.value.mainid) {
    getMainColumns(unionForm.value.mainid,maincolumnList);
  }
  if(unionForm.value.unionmainid) {
    getMainColumns(unionForm.value.unionmainid,unioncolumnList);
  }

  if(typeof unionForm.value.showpos != 'object' ) {
    unionForm.value.showpos = eval(unionForm.value.showpos);
  }
};


defineExpose({
  openDialog
});




</script>
