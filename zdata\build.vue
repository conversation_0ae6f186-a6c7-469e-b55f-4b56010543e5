<template>
  <div class="build">
    <contentmenu ref="contentmenu"></contentmenu>
    <imglist ref="imglist"
             @change="handleSetimg"></imglist>
    <!-- <headers></headers> -->
    <top ref="top"></top>
    <div class="app"
         :class="{'app--none':!menuFlag}">
      <div class="menu"
           v-show="menuFlag&&menuShow"
           @click.self="handleMouseDown">
        <p class="title">图层</p>
        <layer ref="layer"
               :nav="nav"></layer>
      </div>
      <!-- 中间区域 -->
      <div ref="section"
           class="section">
        <div class="refer-line-img"
             @click="imgClick">
          <img :src="isShowReferLine?imgOpenData:imgClose">
        </div>
        <sketch-rule :thick="thick"
                     :scale="scale"
                     :width="width"
                     :height="height"
                     :startX="startX"
                     :startY="startY"
                     :isShowReferLine="isShowReferLine"
                     :palette="palette"
                     :shadow="shadow"
                     :horLineArr="lines.h"
                     :verLineArr="lines.v" />
        <div ref='screensRef'
             id="screens"
             :class="dragSlide?'dragghanle':''"
             @mousedown.stop="dragMousedown"
             @mouseup="dragMouseup"
             @mousemove="dragMousemove"
             @wheel="handleWheel"
             @keydown.delete="handleDeleteSelect"
             @scroll="handleScroll">
          <div ref="containerRef"
               class="screen-container">
            <div class="canvas"
                 ref="canvas"
                 :style="canvasStyle">
              <container ref="container" :status="1"
                         :wscale="scale"></container>
            </div>
          </div>
        </div>
      </div>
      <div class="menu params"
           v-show="menuFlag&&paramsShow">
        <!-- <p class="title">操作</p> -->
        <br/>
        <el-tabs class="tabs"
                 stretch
                 v-model="tabsActive">
          <el-tab-pane name="0">
            <el-tooltip slot="label"
                        effect="dark"
                        content="配置"
                        disabled="true"
                        placement="top">
              <div><i class="el-icon-setting"> 配置</i></div>
            </el-tooltip>
            <el-form label-width="110px"
                     label-position="left"
                     size="mini">
              <!-- 组件配置 -->
              <template v-if="!vaildProp('',[undefined])">
                <p class="title">{{activeObj.title}}</p>
                <el-form-item label="图层名称">
                  <avue-input v-model="activeObj.name"></avue-input>
                </el-form-item>
                <el-form-item label="隐藏">
                  <avue-switch v-model="activeObj.display"></avue-switch>
                </el-form-item>
                <el-form-item label="锁定">
                  <avue-switch v-model="activeObj.lock"></avue-switch>
                </el-form-item>
                <template v-if="vaildProp('colorList')">
                  <el-form-item label="系统配色">
                    <avue-switch v-model="activeOption.switchTheme"></avue-switch>
                  </el-form-item>
                </template>
                <el-form-item label="主题选择"
                              v-if="activeOption.switchTheme">
                  <avue-select v-model="activeOption.theme"
                               :dic="dicOption.themeList">
                  </avue-select>
                </el-form-item>
                <el-form-item v-if="vaildProp('echartsList')" label="echarts参数" >
                  <el-button size="mini"
                             type="primary"
                             @click="openCode('echartsOption')">编辑</el-button>
                </el-form-item>
                <component :is="activeComponent.prop+'Option'"></component>
                <main-option></main-option>
              </template>
              <!-- 多选配置选项 -->
              <template v-else-if="isSelectActive">
                <el-form-item label="水平方式">
                  <el-tooltip content="左对齐"
                              placement="top">
                    <i class="el-icon-s-fold icon"
                       @click="$refs.container.handlePostionSelect('left')"></i>
                  </el-tooltip>
                  <el-tooltip content="居中对齐"
                              placement="top">
                    <i class="el-icon-s-operation icon"
                       @click="$refs.container.handlePostionSelect('center')"></i>
                  </el-tooltip>
                  <el-tooltip content="右对齐"
                              placement="top">
                    <i class="el-icon-s-unfold icon"
                       @click="$refs.container.handlePostionSelect('right')"></i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="垂直方式">
                  <el-tooltip content="顶对齐"
                              placement="top">
                    <i class="el-icon-s-fold icon"
                       @click="$refs.container.handlePostionSelect('top')"></i>
                  </el-tooltip>
                  <el-tooltip content="中部对齐"
                              placement="top">
                    <i class="el-icon-s-operation icon"
                       @click="$refs.container.handlePostionSelect('middle')"></i>
                  </el-tooltip>
                  <el-tooltip content="底对齐"
                              placement="top">
                    <i class="el-icon-s-unfold icon"
                       @click="$refs.container.handlePostionSelect('bottom')"></i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label-width="0">
                  <el-button type="primary"
                             size="mini"
                             class="block"
                             @click="handleFolder">成组</el-button>
                </el-form-item>
                <el-form-item label-width="0">
                  <el-button type="danger"
                             size="mini"
                             class="block"
                             @click="handleDeleteSelect">删除</el-button>
                </el-form-item>
              </template>
              <!-- 主屏的配置项 -->
              <template v-else>
                <el-form-item label="大屏宽度">
                  <avue-input-number v-model="config.width"></avue-input-number>
                </el-form-item>
                <el-form-item label="大屏高度">
                  <avue-input-number v-model="config.height"></avue-input-number>
                </el-form-item>

                <el-form-item label="大屏简介">
                  <avue-input v-model="config.info"
                              type="textarea"
                              :min-rows="5"></avue-input>
                </el-form-item>
                <el-form-item label="背景颜色">
                  <avue-input-color v-model="config.backgroundColor"></avue-input-color>
                </el-form-item>
                <el-form-item label="背景图片">
                  <img :src="config.backgroundImage"
                       @click="handleOpenImg('config.backgroundImage','background')"
                       alt=""
                       width="100%" />
                  <el-input clearable
                            v-model="config.backgroundImage">
                    <div @click="handleOpenImg('config.backgroundImage','background')"
                         slot="append">
                      <i class="iconfont icon-img"></i>
                    </div>
                  </el-input>
                </el-form-item>
                <el-form-item label="解锁Y轴">
                  <avue-switch v-model="config.locky"></avue-switch>
                </el-form-item>
                <el-form-item label="公共地址">
                  <avue-input type="textarea"
                              :min-rows="3"
                              v-model="config.url"></avue-input>
                </el-form-item>
                <el-form-item label="公共请求参数">
                  <el-button size="mini"
                             type="primary"
                             @click="openCode('query','公共请求参数')">编辑函数</el-button>
                </el-form-item>
                <el-form-item label="公共请求头">
                  <el-button size="mini"
                             type="primary"
                             @click="openCode('header','公共请求头')">编辑函数</el-button>
                </el-form-item>
                <el-form-item label="大屏水印">
                  <avue-switch v-model="config.mark.show"></avue-switch>
                </el-form-item>
                <template v-if="config.mark.show">
                  <el-form-item label="内容">
                    <avue-input v-model="config.mark.text"></avue-input>
                  </el-form-item>
                  <el-form-item label="字体大小">
                    <avue-input-number v-model="config.mark.fontSize"></avue-input-number>
                  </el-form-item>
                  <el-form-item label="颜色">
                    <avue-input-color v-model="config.mark.color"></avue-input-color>
                  </el-form-item>
                  <el-form-item label="角度">
                    <avue-input-number v-model="config.mark.degree"></avue-input-number>
                  </el-form-item>
                </template>
              </template>
            </el-form>
          </el-tab-pane>
          <!-- 数据配置 -->
          <el-tab-pane name="1"
                       v-if="vaildProp('dataList')">
            <el-tooltip slot="label"
                        effect="dark"
                        content="数据"
                        disabled="true"
                        placement="top">
              <div><i class="el-icon-document-copy"> 数据</i></div>
            </el-tooltip>
            <el-form label-width="90px"
                     label-position="left"
                     size="mini">
              <el-form-item label="数据类型">
                <avue-radio v-model="activeObj.dataType"
                            :dic="dicOption.dataType"></avue-radio>
              </el-form-item>
              <template v-if="isStatic">
                <el-form-item label="数据值"
                              label-position="top">
                  <el-upload :show-file-list="false"
                             :auto-upload="false"
                             accept=".xls,.xlsx"
                             :on-change="handleImport">
                    <el-button size="mini"
                               type="success">导入数据(Excel)</el-button>
                  </el-upload>
                </el-form-item>
                <el-form-item label-width="0" >
                  <el-button size="mini"
                             type="primary"
                             class="block"
                             @click="openCode('data','静态数据')">编辑静态数据</el-button>
                </el-form-item>
              </template>
              <template v-else>
                <template v-if="isApi">
                  <el-form-item label="接口地址">
                    <avue-input type="textarea" minRows="1" rows="2" v-model="activeObj.url"></avue-input>
                  </el-form-item>
                  <el-form-item label="请求方式">
                    <el-radio-group v-model="activeObj.dataMethod">
                      <el-radio-button v-for="item in dicOption.dataMethod"
                                       :key="item.value"
                                       :label="item.value"
                      >{{item.label}}
                      </el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label-width="0" >
                    <el-button size="mini"
                               type="danger"
                               class="block"
                               @click="openCode('dataHeader')">编辑请求头</el-button>
                  </el-form-item>
                </template>
                <template v-if="isWs">
                  <el-form-item label="WS地址">
                    <avue-input type="textarea" minRows="1" rows="2" v-model="activeObj.wsUrl"></avue-input>
                  </el-form-item>
                </template>

                <template v-if="isBi">
                  <el-form-item label="数据对象">
                    <avue-select placeholder="请选择数据对象" style="width: 100%"
                                 v-model="activeObj.kmain" :dic="biList" :props="biProps" @change="handleSelectbi">
                    </avue-select>
                  </el-form-item>
                  <el-form-item label="维度" >
                    <avue-select placeholder="请选择维度"  style="width: 100%"
                                 v-model="activeObj.dims" :dic="dimList" :props="dimProps">
                    </avue-select>
                  </el-form-item>
                  <el-form-item label="指标" >
                    <avue-select placeholder="请选择指标" multiple style="width: 100%"
                                 v-model="activeObj.vals" :dic="valList" :props="dimProps">
                    </avue-select>
                  </el-form-item>
                </template>

                <template v-if="isSql">
                  <el-form-item label="数据源">
                    <avue-select placeholder="请选择数据源" style="width: 100%;height:28px"
                                 v-model="activeObj.dataDatasource" :dic="datasourceList" :props="datasourceProps">
                    </avue-select>
                  </el-form-item>
                  <el-form-item label-width="100" label="SQL语句" >
                    <el-input type="textarea"
                              :min-rows="2"  :rows="10"
                              v-model="activeObj.dataSql"></el-input>
                  </el-form-item>
                </template>

                <el-form-item label-width="0" >
                  <el-button size="mini"
                             type="primary"
                             class="block"
                             @click="openCode('dataQuery')">编辑查询参数</el-button>
                </el-form-item>
                <el-form-item label-width="0" >
                  <el-button size="mini"
                             type="warning"
                             class="block"
                             @click="openData(true)">查看处理前数据</el-button>
                </el-form-item>
                <el-form-item label-width="0" >
                  <el-button size="mini"
                             type="primary"
                             class="block"
                             @click="openCode('dataFormatter')">数据处理</el-button>
                </el-form-item>
                <el-form-item label-width="0" >
                  <el-button size="mini"
                             type="danger"
                             class="block"
                             @click="openData(false)">查看处理后数据</el-button>
                </el-form-item>
              </template>

              <el-form-item label-width="0">
                <el-button size="mini"
                           type="danger"
                           class="block"
                           @click="openCode('stylesFormatter','编辑样式')">编辑样式</el-button>
              </el-form-item>
              <el-form-item label="刷新时间" v-if="!isWs && !isStatic">
                <avue-input-number v-model="activeObj.time"></avue-input-number>
              </el-form-item>
              <el-form-item label-width="0">
                <el-button size="mini"
                           type="primary"
                           class="block"
                           @click="handleRes">刷新数据</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <!-- 交互事件配置 -->
          <el-tab-pane name="2"
                       v-if="vaildProp('eventList')">
            <el-tooltip slot="label"
                        effect="dark"
                        content="交互"
                        disabled="true"
                        placement="top">
              <div><i class="el-icon-thumb"> 交互</i></div>
            </el-tooltip>
            <el-form label-width="90px"
                     label-position="left"
                     size="mini">
              <el-form-item label="子类">
                <avue-select multiple
                             v-model="activeObj.child.index"
                             :dic="childList"
                             :props="childProps">
                </avue-select>
              </el-form-item>
              <el-form-item label="参数名称">
                <avue-input v-model="activeObj.child.paramName"></avue-input>
              </el-form-item>
              <el-form-item label="映射字段">
                <avue-input v-model="activeObj.child.paramValue"
                            placeholder="默认为value"></avue-input>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <!-- 其他事件配置 -->
          <el-tab-pane name="3"
                       v-if="!vaildProp('',[undefined])">
            <el-tooltip slot="label"
                        effect="dark"
                        content="事件"
                        disabled="true"
                        placement="top">
              <div><i class="iconfont icon-peizhi"> 事件</i></div>
            </el-tooltip>
            <el-form label-width="90px"
                     label-position="left"
                     size="mini">
              <el-form-item label="点击事件">
                <el-button size="mini"
                           type="primary"
                           @click="openCode('clickFormatter','点击事件')">编辑</el-button>
              </el-form-item>
              <el-form-item label="提示事件"
                            v-if="vaildProp('labelFormatterList')">
                <el-button size="mini"
                           type="primary"
                           @click="openCode('formatter','提示事件')">编辑</el-button>
              </el-form-item>
              <el-form-item label="标题事件"
                            v-if="vaildProp('labelFormatterList')">
                <el-button size="mini"
                           type="primary"
                           @click="openCode('labelFormatter','标题事件')">编辑</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <!-- 基本参数配置 -->
          <el-tab-pane name="4"
                       v-if="isActive">
            <el-tooltip slot="label"
                        effect="dark"
                        content="参数"
                        disabled="true"
                        placement="top">
              <div><i class="el-icon-folder"> 参数</i></div>
            </el-tooltip>
            <el-form label-width="90px"
                     label-position="left"
                     size="mini">
              <el-form-item label="序号">
                <avue-input v-model="activeObj.index"
                            disabled></avue-input>
              </el-form-item>
              <el-form-item label="X位置">
                <avue-input-number v-model="activeObj.left"></avue-input-number>
              </el-form-item>
              <el-form-item label="Y位置">
                <avue-input-number v-model="activeObj.top"></avue-input-number>
              </el-form-item>
              <el-form-item label="宽度">
                <avue-input-number v-model="activeComponent.width"></avue-input-number>
              </el-form-item>
              <el-form-item label="高度">
                <avue-input-number v-model="activeComponent.height"></avue-input-number>
              </el-form-item>
              <el-form-item label="定位">
                <avue-radio filterable
                            allow-create
                            v-model="activeComponent.position"
                            :dic="dicOption.positionList"></avue-radio>
              </el-form-item>
              <el-form-item label="透视">
                <avue-slider v-model="activeComponent.perspective"
                             :max="1000"></avue-slider>
              </el-form-item>
              <el-form-item label="缩放">
                <avue-slider v-model="activeComponent.scale"
                             :max="10"></avue-slider>
              </el-form-item>
              <el-form-item label="透明度">
                <avue-slider :step="0.1"
                             v-model="activeComponent.opacity"
                             :max="1"></avue-slider>
              </el-form-item>
              <el-form-item label="进入动画">
                <avue-select filterable
                             allow-create
                             v-model="activeComponent.animated"
                             :dic="dicOption.animated"></avue-select>
                <br/>
                <a href="https://www.dowebok.com/demo/2014/98/"
                   target="_blank">点击查看动画类型</a>
              </el-form-item>

              <el-form-item label="X旋转度">
                <!-- <avue-input-number :min="-360" :max="360" v-model="activeComponent.rotateX"></avue-input-number> -->
                <avue-slider v-model="activeComponent.rotateX" :step="1" :min="-360" :max="360"></avue-slider>
              </el-form-item>
              <el-form-item label="Y旋转度">
                <!-- <avue-input-number :min="-360" :max="360" v-model="activeComponent.rotateY"></avue-input-number> -->
                <avue-slider v-model="activeComponent.rotateY" :step="1" :min="-360" :max="360"></avue-slider>
              </el-form-item>
              <el-form-item label="Z旋转度">
                <!-- <avue-input-number :min="-360" :max="360" v-model="activeComponent.rotateZ"></avue-input-number> -->
                <avue-slider v-model="activeComponent.rotateZ" :step="1" :min="-360" :max="360"></avue-slider>
              </el-form-item>


              <el-form-item label="开启旋转">
                <avue-switch v-model="activeComponent.rotate"></avue-switch>
              </el-form-item>
              <template v-if="activeComponent.rotate">
                <el-form-item label="旋转时间">
                  <avue-input-number v-model="activeComponent.duration"></avue-input-number>
                </el-form-item>

                <el-form-item label="开启3D旋转">
                  <avue-switch v-model="activeComponent.rotate3d"></avue-switch>
                </el-form-item>
                <template v-if="activeComponent.rotate3d">
                  <el-form-item label="旋转至X">
                    <avue-slider v-model="activeComponent.rotatetoX" :step="1" :min="-360" :max="360"></avue-slider>
                    <!-- <avue-input-number :min="-360" :max="360" v-model="activeComponent.rotatetoX"></avue-input-number> -->
                  </el-form-item>
                  <el-form-item label="旋转至Y">
                    <!-- <avue-input-number :min="-360" :max="360" v-model="activeComponent.rotatetoY"></avue-input-number> -->
                    <avue-slider v-model="activeComponent.rotatetoY" :step="1" :min="-360" :max="360"></avue-slider>
                  </el-form-item>
                  <el-form-item label="旋转至Z">
                    <!-- <avue-input-number :min="-360" :max="360" v-model="activeComponent.rotatetoZ"></avue-input-number> -->
                    <avue-slider v-model="activeComponent.rotatetoZ" :step="1" :min="-360" :max="360"></avue-slider>
                  </el-form-item>
                </template>
              </template>

            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <codeedit @submit="codeClose"
              v-if="code.box"
              :title="code.title"
              :type="code.type"
              v-model="code.obj"
              :visible.sync="code.box"></codeedit>
    <el-dialog append-to-body  :title=showTitle
               :close-on-click-modal="false"
               :visible.sync="show"
               width="60%">
      <el-form size="small"
               v-if="show"
               label-width="130px">
        <el-form-item label-width="0" >
          <monaco-editor v-model="dataShow"
                         disabled
                         height="440"></monaco-editor>
        </el-form-item>



        <!--        <template v-if="isStatic">-->
        <!--          <el-form-item label="数据值"-->
        <!--                        label-position="top">-->
        <!--            <el-button size="mini"-->
        <!--                       type="primary"-->
        <!--                       @click="openCode('data','数据值')">编辑JSON</el-button>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="数据值"-->
        <!--                        label-position="top">-->
        <!--            <el-upload :show-file-list="false"-->
        <!--                       :auto-upload="false"-->
        <!--                       accept=".xls,.xlsx"-->
        <!--                       :on-change="handleImport">-->
        <!--              <el-button size="mini"-->
        <!--                         type="success">导入数据(Excel)</el-button>-->
        <!--            </el-upload>-->

        <!--          </el-form-item>-->
        <!--        </template>-->
        <!--        &lt;!&ndash; <template v-else-if="isSql">-->
        <!--          <el-form-item label="数据源选择">-->
        <!--            <avue-select :dic="DIC.sql"-->
        <!--                         v-model="db"></avue-select>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="SQL语句"-->
        <!--                        label-position="top">-->
        <!--            <monaco-editor v-model="sql"-->
        <!--                           language="sql"-->
        <!--                           height="100"></monaco-editor>-->
        <!--          </el-form-item>-->
        <!--        </template> &ndash;&gt;-->


        <!--        <template v-else-if="isApi">-->
        <!--          <el-form-item label="接口地址">-->
        <!--            <avue-input v-model="activeObj.url"></avue-input>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="请求方式">-->
        <!--            <avue-select v-model="activeObj.dataMethod"-->
        <!--                         :dic="dicOption.dataMethod"></avue-select>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="请求头">-->
        <!--            <el-button size="small"-->
        <!--                       type="primary"-->
        <!--                       @click="openCode('dataHeader','请求头')">编辑函数</el-button>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="请求参数">-->
        <!--            <el-button size="small"-->
        <!--                       type="primary"-->
        <!--                       @click="openCode('dataQuery','请求参数')">编辑函数</el-button>-->
        <!--          </el-form-item>-->
        <!--        </template>-->
        <!--        <template v-else-if="isWs">-->
        <!--          <el-form-item label="WS地址">-->
        <!--            <el-input v-model="activeObj.wsUrl">-->
        <!--            </el-input>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="请求参数">-->
        <!--            <el-button size="small"-->
        <!--                       type="primary"-->
        <!--                       @click="openCode('dataQuery','请求参数')">编辑函数-->
        <!--            </el-button>-->
        <!--          </el-form-item>-->
        <!--        </template>-->
        <!--        <el-form-item label="响应数据">-->
        <!--          <monaco-editor v-model="dataRes"-->
        <!--                         disabled-->
        <!--                         height="300"></monaco-editor>-->
        <!--        </el-form-item>-->
      </el-form>
      <!--      <span slot="footer"-->
      <!--            class="dialog-footer">-->
      <!--        <el-button size="small"-->
      <!--                   type="danger"-->
      <!--                   @click="openCode('dataFormatter')">数据处理</el-button>-->
      <!--        <el-button size="small"-->
      <!--                   type="primary"-->
      <!--                   @click="handleRes">刷新数据</el-button>-->
      <!--      </span>-->
    </el-dialog>
  </div>

</template>
<script setup lang="ts">
import SketchRule from 'vue3-sketch-ruler'
import 'vue3-sketch-ruler/lib/style.css'
// ts需要时引入类型
import type { SketchRulerProps } from 'vue3-sketch-ruler'

</script>
<style scoped lang="scss">

</style>
