import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 获取回调列表
export const getCallbackList = (mid) => {
  return http.get(PORT1 + `/../dev/callback/getCallbackList?mid=` + mid );
};


// 保存回调
export const save = (params) => {
  return http.post(PORT1 + `/../dev/callback/save` ,params);
};

// 删除回调
export const del = (id) => {
  return http.get(PORT1 + `/../dev/callback/del?id=` + id );
};





