<template>
  <div class="data-card" :style="cardStyle">
    <div class="card-header" v-if="config.showTitle">
      <h3 class="card-title">{{ config.title || '数据卡片' }}</h3>
    </div>
    <div class="card-content">
      <div class="card-value">
        <span class="value-number">{{ formatValue(config.value || 0) }}</span>
        <span class="value-unit" v-if="config.unit">{{ config.unit }}</span>
      </div>
      <div class="card-label">{{ config.label || '数据指标' }}</div>
      <div class="card-trend" v-if="config.showTrend">
        <el-icon :class="trendClass">
          <component :is="trendIcon" />
        </el-icon>
        <span class="trend-text">{{ config.trendText || '较昨日' }}</span>
        <span class="trend-value" :class="trendClass">{{ config.trendValue || '+12.5%' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue';

interface Props {
  config?: {
    title?: string;
    value?: number;
    unit?: string;
    label?: string;
    showTitle?: boolean;
    showTrend?: boolean;
    trendText?: string;
    trendValue?: string;
    trendType?: 'up' | 'down' | 'flat';
    backgroundColor?: string;
    textColor?: string;
  };
  style?: any;
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({
    title: '数据卡片',
    value: 1234,
    unit: '',
    label: '数据指标',
    showTitle: true,
    showTrend: true,
    trendText: '较昨日',
    trendValue: '+12.5%',
    trendType: 'up',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    textColor: '#ffffff'
  })
});

const cardStyle = computed(() => ({
  backgroundColor: props.config?.backgroundColor || 'rgba(64, 158, 255, 0.1)',
  color: props.config?.textColor || '#ffffff',
  ...props.style
}));

const trendIcon = computed(() => {
  switch (props.config?.trendType) {
    case 'up':
      return ArrowUp;
    case 'down':
      return ArrowDown;
    default:
      return Minus;
  }
});

const trendClass = computed(() => {
  switch (props.config?.trendType) {
    case 'up':
      return 'trend-up';
    case 'down':
      return 'trend-down';
    default:
      return 'trend-flat';
  }
});

const formatValue = (value: number) => {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + '万';
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'k';
  }
  return value.toString();
};
</script>

<style lang="scss" scoped>
.data-card {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  padding: 20px;
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid rgba(64, 158, 255, 0.3);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .card-header {
    margin-bottom: 15px;

    .card-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: inherit;
    }
  }

  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .card-value {
      display: flex;
      align-items: baseline;
      margin-bottom: 10px;

      .value-number {
        font-size: 32px;
        font-weight: 600;
        line-height: 1;
        color: inherit;
      }

      .value-unit {
        font-size: 16px;
        margin-left: 5px;
        color: rgba(255, 255, 255, 0.7);
      }
    }

    .card-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 15px;
    }

    .card-trend {
      display: flex;
      align-items: center;
      font-size: 12px;

      .trend-text {
        margin: 0 5px;
        color: rgba(255, 255, 255, 0.7);
      }

      .trend-value {
        font-weight: 500;

        &.trend-up {
          color: #67c23a;
        }

        &.trend-down {
          color: #f56c6c;
        }

        &.trend-flat {
          color: #e6a23c;
        }
      }

      .el-icon {
        &.trend-up {
          color: #67c23a;
        }

        &.trend-down {
          color: #f56c6c;
        }

        &.trend-flat {
          color: #e6a23c;
        }
      }
    }
  }
}
</style>
