<template>
  <div class="data-table" :style="tableStyle">
    <div class="table-header" v-if="config.showTitle">
      <h3 class="table-title">{{ config.title || '数据表格' }}</h3>
    </div>
    <div class="table-content">
      <el-table
        :data="tableData"
        :height="tableHeight"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
        :row-style="rowStyle"
      >
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :align="column.align || 'center'"
        >
          <template #default="{ row, column: col }">
            <span v-if="col.property === 'rank'" class="rank-cell">
              {{ row[col.property] }}
            </span>
            <span v-else-if="col.property === 'trend'" class="trend-cell">
              <el-icon :class="getTrendClass(row[col.property])">
                <component :is="getTrendIcon(row[col.property])" />
              </el-icon>
              {{ row[col.property] }}
            </span>
            <span v-else>{{ row[col.property] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue';

interface Props {
  config?: {
    title?: string;
    showTitle?: boolean;
    data?: any[];
    columns?: any[];
    backgroundColor?: string;
    textColor?: string;
    headerColor?: string;
  };
  style?: any;
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({
    title: '数据表格',
    showTitle: true,
    data: [],
    columns: [],
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    textColor: '#ffffff',
    headerColor: 'rgba(64, 158, 255, 0.3)'
  })
});

const tableStyle = computed(() => ({
  backgroundColor: props.config?.backgroundColor || 'rgba(255, 255, 255, 0.05)',
  color: props.config?.textColor || '#ffffff',
  ...props.style
}));

const tableHeight = computed(() => {
  return props.config?.showTitle ? 'calc(100% - 50px)' : '100%';
});

// 默认数据
const defaultData = [
  { rank: 1, name: '北京', value: 12580, trend: '+12.5%' },
  { rank: 2, name: '上海', value: 11240, trend: '+8.3%' },
  { rank: 3, name: '广州', value: 9860, trend: '-2.1%' },
  { rank: 4, name: '深圳', value: 8750, trend: '+15.2%' },
  { rank: 5, name: '杭州', value: 7320, trend: '+5.8%' }
];

const defaultColumns = [
  { prop: 'rank', label: '排名', width: 80, align: 'center' },
  { prop: 'name', label: '城市', width: 120, align: 'left' },
  { prop: 'value', label: '数值', width: 100, align: 'right' },
  { prop: 'trend', label: '趋势', width: 100, align: 'center' }
];

const tableData = computed(() => {
  return props.config?.data && props.config.data.length > 0 
    ? props.config.data 
    : defaultData;
});

const columns = computed(() => {
  return props.config?.columns && props.config.columns.length > 0 
    ? props.config.columns 
    : defaultColumns;
});

const headerCellStyle = computed(() => ({
  backgroundColor: props.config?.headerColor || 'rgba(64, 158, 255, 0.3)',
  color: props.config?.textColor || '#ffffff',
  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
  fontSize: '14px',
  fontWeight: '500'
}));

const cellStyle = computed(() => ({
  backgroundColor: 'transparent',
  color: props.config?.textColor || '#ffffff',
  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
  fontSize: '13px'
}));

const rowStyle = computed(() => ({
  backgroundColor: 'transparent'
}));

const getTrendIcon = (trend: string) => {
  if (trend.startsWith('+')) return ArrowUp;
  if (trend.startsWith('-')) return ArrowDown;
  return Minus;
};

const getTrendClass = (trend: string) => {
  if (trend.startsWith('+')) return 'trend-up';
  if (trend.startsWith('-')) return 'trend-down';
  return 'trend-flat';
};
</script>

<style lang="scss" scoped>
.data-table {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);

  .table-header {
    margin-bottom: 15px;

    .table-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: inherit;
    }
  }

  .table-content {
    height: calc(100% - 50px);

    :deep(.el-table) {
      background: transparent;
      color: inherit;

      .el-table__header-wrapper {
        .el-table__header {
          background: transparent;
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          background: transparent;
        }
      }

      .el-table__empty-block {
        background: transparent;
        color: rgba(255, 255, 255, 0.6);
      }

      .el-table__row {
        &:hover {
          background: rgba(64, 158, 255, 0.1) !important;
        }
      }
    }

    .rank-cell {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: linear-gradient(135deg, #409eff, #67c23a);
      color: #fff;
      font-size: 12px;
      font-weight: 600;
    }

    .trend-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      .el-icon {
        &.trend-up {
          color: #67c23a;
        }

        &.trend-down {
          color: #f56c6c;
        }

        &.trend-flat {
          color: #e6a23c;
        }
      }
    }
  }
}
</style>
