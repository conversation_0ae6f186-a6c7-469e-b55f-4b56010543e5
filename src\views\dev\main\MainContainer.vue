<template>
  <div ref="divDom" class="main-box" :style="{'height': 'calc(100vh - 230px)','background-color': '#ffffff'}">
    <el-form
      ref="formRef"
      :model="obj"
      status-icon
      :rules="columnRules"
      label-suffix=":"
      size="large"
      :hide-required-asterisk="false"
      label-width="30%"
      style="width:70%;padding-top:20px"
    >
      <el-form-item label="名称" prop="name" >
        <el-input v-model="obj.name"></el-input>
      </el-form-item>
      <el-form-item label="显示名" prop="showname" >
        <el-input v-model="obj.showname"></el-input>
      </el-form-item>
      <el-form-item label="数据库" prop="dbname" >
        <el-text type="primary">{{obj.dbname}}</el-text>
      </el-form-item>
      <el-form-item label="表名" prop="tablename" >
        <el-input v-model="obj.tablename"></el-input>
      </el-form-item>
      <template v-if="obj.type==1">
        <el-form-item label="类型" prop="type" >
          <el-tag type="success" >BI - 数据报表</el-tag>
        </el-form-item>

      </template>
      <template v-else>
        <el-form-item label="类型" prop="type" >
          <el-tag type="primary" >MIS - 管理信息系统</el-tag>
        </el-form-item>
        <el-form-item label="主键" prop="pkfield" >
          <el-select v-model="obj.pkfield">
            <el-option v-for="item in columns" :key="item.name" :label="item.showname?item.showname+'/'+item.name:item.name" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主键生成" prop="seq"  >
          <el-select v-model="obj.seq" filterable allow-create>
            <el-option  value="AUTO"></el-option>
            <el-option  value="UUID"></el-option>
            <el-option  value="RAND8"></el-option>
            <el-option  value="seq_default"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <div style="text-align:right">
        <el-button type="danger" plain @click="delMain">删除</el-button>
        <el-button type="primary" plain @click="rebuildMain">重建</el-button>
        <el-button type="success" plain @click="cloneMain">克隆</el-button>
        <el-button type="primary" @click="saveMain(formRef)">保存</el-button>
      </div>
    </el-form>

  </div>
</template>
<script lang="ts" setup>
import { ref,reactive,inject } from 'vue'
import { getMainDetail } from '@/api/dev/main';
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";
import {save,del,rebuild,clone} from "@/api/dev/main";
import {delDs} from "@/api/dev/ds";

const columnRules = reactive({
  name: [{ required: true, message: "请输入名称" }],
  showname: [{ required: true, message: "请输入显示名" }],
  tablename: [{ required: true, message: "请输入表名" }],
});

const curMain = inject('curMain')

const obj = ref({})
const columns = ref([]);

const refresh = async () => {
  const { data } = await getMainDetail(curMain?.id);

  obj.value = data?.main
  obj.value.dbname = curMain.dbname;

  columns.value = data.columns;
}

defineExpose({
  refresh
});



const formRef = ref();

const delMain = async () => {
  if(!curMain?.id) return false;

  ElMessageBox.confirm(`是否删除该数据对象?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const resp = await del(curMain?.id) ;

    if(resp.code === 200) {
      ElMessage.success({ message: resp.msg });
    }else {
      ElMessage.error({ message: resp.msg });
    }
  }).catch(()=>{});
}

const rebuildMain = async () => {
  if(!curMain?.id) return false;

  ElMessageBox.confirm(`是否重建该数据对象?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const resp = await rebuild(curMain.id) ;

    if(resp.code === 200) {
      ElMessage.success({ message: resp.msg });
    }else {
      ElMessage.error({ message: resp.msg });
    }
  }).catch(()=>{});
}


const cloneMain = async () => {
  if(!curMain.id) return false;


  ElMessageBox.confirm(`是否克隆该数据对象?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const resp = await clone(curMain.id) ;

    if(resp.code === 200) {
      ElMessage.success({ message: resp.msg });
    }else {
      ElMessage.error({ message: resp.msg });
    }
  }).catch(()=>{});

}

const saveMain = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const resp = await save(obj.value) ;

      if(resp.code == 200) {
        ElMessage.success({ message: resp.msg });
        Object.assign(curMain,obj.value)
      }else {
        ElMessage.error({ message: resp.msg });
      }

    } catch (error) {
      console.log(error);
    }
  });
}

</script>
