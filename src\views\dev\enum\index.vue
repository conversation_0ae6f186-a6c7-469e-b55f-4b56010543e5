<template>
  <div class="main-box">
    <div class="card filter">
      <h4 class="title sle">枚举
        <el-button style="float:right;margin-left:5px" circle :icon="icons.Refresh" @click="getZenum" title="刷新" />
        <el-button style="float:right" circle :icon="icons.Plus"  @click="addZenum" title="新增枚举" />
      </h4>
      <el-input v-model="filterText" placeholder="输入关键字进行过滤" clearable />
      <el-scrollbar :style="{ height: `calc(100% - 107px)`  }">
        <template v-for="zenum in zenumData">
        <div v-if="showItem(zenum)"
             :id="zenum?.id"
             v-on:click="handleZenumClick(zenum)"
             class="zenumNode"
             :class="classObject(zenum?.id)" style="display:flex">
          <span style="display:inline-block;width:60px;height:90px;margin:5px">
            <div>
              <el-tag effect="dark"  type="primary" v-if="zenum.devid==0">公有</el-tag>
              <el-tag effect="dark"  type="success" v-else>私有</el-tag>
            </div>
            <div>
              <el-tag effect="dark"  type="warning" v-if="zenum.type=='li'">列表</el-tag>
              <el-tag effect="dark"  type="danger" v-else>SQL</el-tag>
            </div>

          </span>
          <span class="zenumInfo" >
            <div>{{ zenum.name }}</div>
            <div>{{ zenum.showname }}</div>
          </span>
        </div>
        </template>
      </el-scrollbar>
    </div>

    <div class="table-box" style="background-color:#ffffff">
      <el-form
        ref="formRef"
        :model="currentZenum"
        status-icon
        :rules="zenumRules"
        label-suffix=":"
        :hide-required-asterisk="false"
        label-width="140" class="card"
        style="width:100%;padding-top:20px;height:100%;margin-left: 5px;"
      >
        <el-row >
          <el-col :span="24">
            <el-form-item label="" label-width="0" >
              <h2 v-if="currentZenum.devid==0">查看枚举（公有枚举不可编辑）</h2>
              <h2 v-else-if="currentZenum.id">编辑枚举</h2>
              <h2 v-else>新增枚举</h2>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="名称" prop="name" >
              <el-input v-model="currentZenum.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示名" prop="showname" >
              <el-input v-model="currentZenum.showname"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型"  >
              <el-radio-group v-model="currentZenum.type" size="default">
                <el-radio-button  label="列表 LI" value="li"  />
                <el-radio-button  label="SQL 语句" value="sql"  />
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="currentZenum.type=='sql'">
            <el-form-item label="数据源" prop="dsid" >
              <el-select v-model="currentZenum.dsid" clearable >
                <el-option v-for="(t) in dsList" :key="t?.id" :value="t?.id" :label="t.type + '-' + t.name + '  :  ' + t.ip"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="枚举数据"  >
              <MonacoEditor v-model="currentZenum.data" language="text" style="height:150px"/>
              <el-tooltip
                class="box-item"
                effect="dark"
                placement="right-start"
              >
                <template #content> li格式以key:value1:value2:...一行，分隔符为:,;\t|，sql格式以select key,value1,value2,... from tab为模板<br />单参数以#替代，单参数或多参数都可以@param@替代 </template>
                <el-icon style="margin-left:20px"><QuestionFilled /></el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="枚举参数" >
              <el-input v-model="currentZenum.param"></el-input>
              <el-tooltip
                class="box-item"
                effect="dark"
                placement="right-start"
              >
                <template #content>  枚举参数可以替换掉枚举数据中的#号，或者@param@ <br />多个参数以分号“;”隔开
                </template>
                <el-icon style="margin-left:20px"><QuestionFilled /></el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="缓存机制" >
              <el-select allow-create filterable default-first-option	 v-model="currentZenum.reload">
                <el-option v-for="(item) in cacheList" :key="item" :value="item" :label="item"></el-option>
              </el-select>
              <el-tooltip
                class="box-item"
                effect="dark"
                placement="right-start"
              >
                <template #content>   -1：不缓存<br />0：永久缓存<br />其他：秒数（3600表示一小时，86400表示一天）
                </template>
                <el-icon style="margin-left:20px"><QuestionFilled /></el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <template v-if="currentZenum.id">
            <el-col :span="18">
              <el-form-item label="数据预览" >
                <el-select multiple	 >
                  <el-option v-for="(item) in testList" :key="item.key" :value="item.key" :label="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="" label-width="0" >
                <el-button type="success" @click="test">test</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24" >
              <el-form-item label="调用地址" >
                <el-link type="primary" target="_blank" :href="'/boots/enumdata/'+currentZenum.id">/boots/enumdata/{{currentZenum.id}}</el-link>
              </el-form-item>
            </el-col>

          </template>

          <el-col :span="12">
          </el-col>
          <el-col :span="12">
            <el-form-item label=""  >
              <el-button type="success" v-if="currentZenum.id" @click="cloneEnum">克隆</el-button>
              <el-button type="primary" v-if="currentZenum.devid!=0" @click="saveEnumAction">保存</el-button>
              <el-button type="danger" v-if="currentZenum.devid!=0 && currentZenum.id" @click="delEnum">删除</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </div>
  </div>
</template>

<script setup lang="tsx" name="zenumIndex">
import {onMounted, reactive, ref, watch} from "vue";
import {ElMessage, ElMessageBox, ElNotification, FormRules, FormInstance} from "element-plus";
import * as icons from "@element-plus/icons-vue";
import {getEnumList, save, del, data} from "@/api/dev/enum";
import {getDsList} from "@/api/dev/ds";
import UploadImg from "@/components/Upload/Img.vue";
import {Ds} from "@/api/interface";
import ProTable from "@/components/ProTable/index.vue";
import MonacoEditor from '@/components/MonacoEditor/index.vue'


const cacheList = [0,-1,3600,86400];

const dsList = ref([]);

onMounted(async () => {
  getZenum();
  const { data } = await getDsList();
  dsList.value = data;
});


const zenumData = ref<{ [id: string]: any }[]>([]);


const filterText = ref("");

const showItem = (zenum) => {
  if(filterText.value == "") return true;

  if(zenum.name!=null && zenum.name.indexOf(filterText.value)>=0) return true;
  if(zenum.showname!=null && zenum.showname.indexOf(filterText.value)>=0) return true;
  return false;
}

const getZenum = async (id) => {
  const { data } = await getEnumList();

  zenumData.value = [...data[0],...data[1]];

  if(!data || data.length==0) return;

  if(id) {
    for(var i=0;i<data.length;i++) {
      if(data[i]?.id==id) {
        Object.assign(currentZenum.value,zenumData.value[i]);
      }
    }
  }else {
    Object.assign(currentZenum.value,zenumData.value[0]);
  }
};



const currentZenum = ref({});

const handleZenumClick = (zenum) => {
  Object.assign(currentZenum.value,zenum);
}

const classObject = (id) => {
  return currentZenum.value?.id == id  ? "zenumSelected" : "zenumNoselected";
}

const delEnum = async () => {
  ElMessageBox.confirm(`是否删除该枚举?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const res = await del(currentZenum.value.id);
    ElMessage({
      type: "success",
      message: `删除枚举成功!`
    });
    getZenum()
  }).catch(()=>{});
}

const zenumTabName = ref("base");
const defineTabName = ref("user");

const addZenum = () => {
  currentZenum.value = {
    showname : '',
    type : 'li',
  };
}


const zenumRules = reactive({
  // type: [{ required: true,trigger: 'change', message: "请选择一个数据库类型" }],
  name: [{ required: true, message: "请输入枚举名称" }],
  showname: [{ required: true, message: "请输入枚举标题" }],
});



const formRef = ref<FormInstance>()


const saveEnumAction = async () => {

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const resp = await save(currentZenum.value);

      if(resp.code==200) {
        ElMessage.success({ message: resp.msg });

        getZenum(resp.data)

      }else {
        ElMessage.error({ message: resp.msg });
      }


    } catch (error) {
      console.log(error);
    }
  });

}

const cloneEnum = () => {
  currentZenum.value.id = null;
  currentZenum.value.devid = null;
}


const testList = ref([]);

const test = async () =>  {
  const resp = await data(currentZenum.value.id);
  console.log(resp);
  testList.value = resp;
}

</script>
<style scoped lang="scss">
  .zenumSelected{
    border: 1px solid var(--el-color-primary);
  }
  .zenumNoselected{
    border: 1px solid rgba(0,0,0,0);
  }
  .zenumNode{
    height: 60px;
    font-size: 14px;
    margin: 8px 2px;
    overflow: hidden; /* 确保文本超出容器时会被裁剪 */
    white-space: nowrap; /* 确保文本不会换行 */
    text-overflow: ellipsis; /* 在文本末尾显示省略号 */
    border-bottom: 1px solid var(--el-color-primary-light-3);
  }
  .zenumInfo {
    width: 110px;height:66px; display: inline-block;margin-left: 5px;
    overflow: hidden; /* 确保文本超出容器时会被裁剪 */
    white-space: nowrap; /* 确保文本不会换行 */
    text-overflow: ellipsis; /* 在文本末尾显示省略号 */
  }
</style>

