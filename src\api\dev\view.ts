import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

export const getViewList = (mid) => {
  return http.get(PORT1 + `/../dev/view/getViewList?mid=` + mid );
};

export const getSelectedViewList = () => {
  return http.get(PORT1 + `/../dev/view/getSelectedViewList` );
};

export const addView = (params) => {
  return http.post(PORT1 + `/../dev/view/add`, params );
};

export const delView = (key) => {
  return http.get(PORT1 + `/../dev/view/del?key=` + key );
};


export function saveView(data: any) {
  return http.post("/dev/view/save", data);
}

export function cloneView(id) {
  return http.get("/dev/view/clone?id=" + id);
}


export function getView(key: string): any {
  return http.get(`/view/detail?key=${key}`);
}





