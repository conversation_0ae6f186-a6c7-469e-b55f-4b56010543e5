import { createApp } from "vue";
import App from "./App.vue";
// reset style sheet
import "@/styles/reset.scss";
// CSS common style sheet
import "@/styles/common.scss";
// iconfont css
import "@/assets/iconfont/iconfont.scss";
// font css
import "@/assets/fonts/font.scss";
// element css
import "element-plus/dist/index.css";
// element dark css
import "element-plus/theme-chalk/dark/css-vars.css";
// custom element dark css
import "@/styles/element-dark.scss";
// custom element css
import "@/styles/element.scss";
// svg icons
import "virtual:svg-icons-register";
// element plus
import ElementPlus from "element-plus";
// element icons
import * as Icons from "@element-plus/icons-vue";
// custom directives
// import directives from "@/directives/index";
// vue Router
import router from "@/routers/front/index";
// vue i18n
import I18n from "@/languages";
// pinia store
import pinia from "@/stores";
// errorHandler
import errorHandler from "@/utils/errorHandler";
// vue-grid-layout 拖拽布局
import VueGridLayout from "vue-grid-layout" // 引入layout

import  about  from '@/views/about/index.vue';
import lineChart  from '@/views/echarts/lineChart/index.vue';
import waterChart from '@/views/echarts/waterChart/index.vue';




import ZcodeTable from '@/packages/ZcodeTable/index.vue';
import ZcodeTableOption from '@/packages/ZcodeTable/option.vue';

import ZGrid from '@/packages/ZGrid/index.vue';
import ZGridOption from '@/packages/ZGrid/option.vue';



import GUI from "@/components/UI";
import DatavCharts from "@/datav/charts";
import GridLayout from "vue3-drr-grid-layout";
import Icon from '@/form/icon/index.vue';

import Vant, { Locale } from 'vant';
import enUS from 'vant/es/locale/lang/en-US';
import 'vant/lib/index.css';



const app = createApp(App);
app.use(pinia)

app.config.globalProperties.$icons = [];

app.config.errorHandler = errorHandler;

// register the element Icons component
Object.keys(Icons).forEach(key => {
  app.config.globalProperties.$icons.push(key);
  app.component(key, Icons[key as keyof typeof Icons]);
});

Locale.use('en-US', enUS)
app.use(Vant)


app.use(GUI);
app.use(DatavCharts);
app.use(GridLayout);




app.component("about",about);
app.component("lineChart",lineChart);
app.component("waterChart",waterChart);
app.component("ZcodeTable",ZcodeTable);
app.component("ZcodeTableOption",ZcodeTableOption);

app.component("ZGrid",ZGrid);
app.component("ZGridOption",ZGridOption);

app.use(ElementPlus);
// .use(directives)
app.use(router).use(I18n).use(VueGridLayout);

app.component("Icon",Icon);


app.mount("#app");
