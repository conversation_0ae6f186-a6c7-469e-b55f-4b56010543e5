<template>
  <div>
    <!-- 组件切换按钮 -->
    <button @click="currentComponent = 'ComponentA'">显示组件A</button>
    <button @click="currentComponent = 'ComponentB'">显示组件B</button>
    <!-- 动态组件 -->
    <component :is="currentComponent"></component>

  </div>
</template>

<script>
  import { ref, defineComponent } from 'vue';
  import ComponentA from './shouye.vue'; // 假设这是一个组件
  import ComponentB from './ds/index.vue'; // 假设这是另一个组件

  export default defineComponent({
    components: {
      ComponentA,
      ComponentB
    },
    setup() {
      const currentComponent = ref('ComponentA'); // 默认显示的组件

      return {
        currentComponent
      };
    }
  });
</script>
