<template>
  <div class="main-box">
    <div class="card filter" v-if="obj.jsonObj.leftOption.showLeft" :style="{'width': obj.jsonObj.leftOption.leftWidth + 'px'}" >
      <h4 class="title sle" v-if="obj.jsonObj.leftOption.showTitle">{{obj.view.title}}</h4>
      <el-input v-model="filterText" placeholder="输入关键字进行过滤" clearable v-if="obj.jsonObj.leftOption.showSearch" />
      <el-scrollbar :style="{ height: `calc(100% - 97px)`  }">
        <template v-for="item in obj.jsonObj.leftOption.leftApiResp">
          <div v-if="showItem(item)"
               :id="item.id"
               v-on:click="handleItemClick(item.id)"
               :class="classObject(item.id)"
               style="height: 80px;font-size: 14px;margin: 8px 2px">
            <img :src="`/src/assets/db/${item.type}.png`" style="width: 60px; display: inline-block;" alt="">
            <span style="width: 110px;height:60px; display: inline-block;;margin-left: 5px">
              <div>{{ item.name }}</div>
              <div>{{ item.ip }}</div>
            </span>
          </div>
        </template>
      </el-scrollbar>
    </div>

    <div class="table-box" :style="{ 'width': (obj.jsonObj.leftOption.showLeft? 'calc(100% - ' + obj.jsonObj.leftOption.leftWidth + 'px)': '100%') }">
      <div v-if="obj.jsonObj.tableOption.queryView" style="padding-bottom: 20px">
<!--        <er-form-preview-->
<!--          :lang="lang"-->
<!--          :layoutType="layoutType"-->
<!--          @listener="queryHandler"-->
<!--          v-loading="loading"-->
<!--          ref="EReditorRef"/>-->
      </div>
      <ProTable
        ref="proTableRef"
        :row-key="obj.jsonObj.tableOption.rowKey"
        :indent="20"
        :columns="computedColumns"
        :request-api="getTableData"
        :request-auto="requestAuto"
        :init-param="initParam"
        :title="obj.view.title"
        :icon="obj.jsonObj.tableOption.icon"
        :titleColor="obj.jsonObj.tableOption.titleColor"
        :show-title="obj.jsonObj.tableOption.showTitle"
        :pagination="obj.jsonObj.tableOption.pagination"
        :pageable="obj.jsonObj.tableOption.pageable"
        :border="!!obj.jsonObj.tableOption.border"
        :stripe="!!obj.jsonObj.tableOption.stripe"
        :showSummary="!!obj.jsonObj.tableOption.showSummary"
        :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader>
          <template v-for="btn in obj.jsonObj.tableOption.topBtns">
            <el-button v-if="btn.selected" v-bind="btn" :style="[!!btn.txtColor ? 'color:' + btn.txtColor : '']" @click="topBtnHandler(btn)">{{btn.label}}</el-button>
          </template>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <template v-for="btn in obj.jsonObj.tableOption.rowBtns">
            <el-button v-if="btn.selected" v-bind="btn" :style="[!!btn.txtColor ? 'color:' + btn.txtColor : '']" @click="rowBtnHandler(btn,scope.row)" >{{btn.label}}</el-button>
          </template>
        </template>
      </ProTable>
    </div>

    <el-dialog
      v-if="dlgObj.type==='dialog'"
      v-model="dlgObj.show"
      :title="dlgObj.title"
      :destroy-on-close="true"
      :width="dlgObj.width"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      draggable
      :key="dlgObj.key"
    >
      <Preview v-if="dlgObj.show" :hashkey="dlgObj.hashkey" :data="dlgObj.data" @listener="handleListener"></Preview>
    </el-dialog>
    <el-drawer
      v-if="dlgObj.type==='drawer'"
      v-model="dlgObj.show"
      :title="dlgObj.title"
      :destroy-on-close="true"
      :width="dlgObj.width"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :key="dlgObj.key"
    >
      <Preview v-if="dlgObj.show" :hashkey="dlgObj.hashkey" :data="dlgObj.data" @listener="handleListener"></Preview>
    </el-drawer>

  </div>
</template>

<script setup lang="tsx" name="ZGrid">
import {onMounted, reactive, ref, watch, inject, computed, nextTick} from "vue";
import {Ds, ResPage, User} from "@/api/interface";
import { genderType } from "@/utils/dict";
import { useHandleData } from "@/hooks/useHandleData";
import {ElMessage, ElMessageBox, ElNotification} from "element-plus";
import ImportExcel from "@/components/ImportExcel/index.vue";
import * as icons from "@element-plus/icons-vue";
// import { CirclePlus, Delete, EditPen, View, Upload, Grid, Histogram } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import {getUserTreeList, deleteUser, editUser, addUser, getUserStatus, exportUserInfo, BatchAddUser} from "@/api/modules/user";
import {getDsList, getDsTables, saveDs, delDs} from "@/api/dev/ds";
import http from "@/api";
import { Props } from "./interface"
import {PORT1} from "@/api/config/servicePort";
import  ZDialog  from '@/components/ZDialog/index.vue'
// import { erFormPreview } from '@ER/formEditor'
import { getView } from "@/api/dev/view"
import _ from 'lodash-es'
import Preview from '@/views/front/Preview.vue'

const obj = inject('obj',reactive({
  view: {},
  jsonObj: {
    leftOption:{showLeft:false},
    tableOption:{queryView:null}
  },
}));

if(!obj.jsonObj.leftOption)obj.jsonObj.leftOption = {}
if(!obj.jsonObj.tableOption)obj.jsonObj.tableOption = {}


const {
  lang
} = inject('globalConfig')
const layoutType = ref(2)
const loading = ref(true)

const EReditorRef = ref();

const requestAuto = ref(false);

const dlgObj = ref({
  type: 'dialog',
  show: false,
  title: '',
  width: 800,
  hashkey: '',
  key: 1,
  data: null,
  callback: null,
})


const opWindow = (btn,func) => {
  const {action,target,targetSize} = btn
  console.log(action,target,targetSize)
  if(target) {
    dlgObj.value.hashkey = target;
  }else return;

  const title = obj.view.title;

  dlgObj.value.type = action || 'dialog';
  if(title) dlgObj.value.title = title;
  if(targetSize) dlgObj.value.width = targetSize;
  dlgObj.value.key++;
  if(func) {
    dlgObj.value.callback = func
  }else {
    dlgObj.value.callback = null
  }
  dlgObj.value.show = true;
}

onMounted(() => {
  getLeftData();

  // const pageable = proTableRef.value!.pageable;

  // Object.assign(pageable,obj.jsonObj.tableOption.pageable)

  // proTableRef.value!.pageable?.value = obj.jsonObj.tableOption.pageable;

  initQueryView();

  requestAuto.value = !( obj.jsonObj.leftOption.showLeft || obj.jsonObj.tableOption.queryView )

  if(requestAuto.value) {
    if(!obj.jsonObj.tableOption.columns || obj.jsonObj.tableOption.columns.length===0) {
      ElMessage.warning("请选择字段！");
    }else {
      proTableRef.value?.search();
    }
  }

});

const initQueryView = async () => {
  if(!obj.jsonObj.tableOption.queryView) return;

  const { data } = await getView(obj.jsonObj.tableOption.queryView);
  const data0 = JSON.parse(data.json);

  // nextTick(() => {
  //   EReditorRef.value.setData(data0)
  //   loading.value = false
  // })

}


const searchTxtList = ['search','query','搜索' ,'查询', '提交', 'init']
const saveTxtList = ['保存','save','确定','confirm' , '提交','submit']
const resetTxtList = ['reset','重置']
const closeTxtList = ['关闭','取消','close','cancel']

const queryHandler = async ({ type, data }) => {
  if(searchTxtList.includes(type)) {
    const obj = _.merge({},initParam,data)
    Object.assign(initParam,obj)
    proTableRef.value?.search();
  }else {
    Object.assign(data,{});
  }
  console.log(type + " click!")
  console.log(initParam)
}

// 一个计算属性 ref
const computedColumns = computed(() => {
  var list = [{
    type: obj.jsonObj.tableOption.pkType,
    label: '#',
    width: 80,
  }] ;

  if(!obj.jsonObj.tableOption.columns) return list;

  obj.jsonObj.tableOption.columns.forEach((item) => {
    // item.prop = item.name;
    // item.label = item.showname;
    if(item.selected){
      list.push(item)
      // list.push({
      //   prop : item.name,
      //   label: item.showname,
      // });
    }
  })

  return list;
})


const filterText = ref("");

const showItem = (item) => {
  if(filterText.value == "") return true;

  if(item.name!=null && item.name.indexOf(filterText.value)>=0) return true;
  if(item.ip!=null && item.ip.indexOf(filterText.value)>=0) return true;
  return false;
}

const proTableRef = ref();


// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

const getLeftData = async () => {
  if(!obj.jsonObj.showLeft || !obj.jsonObj.leftOption.leftApi) return;

  const { data } = await http.get(obj.jsonObj.leftOption.leftApi, {}, { cancel: false });

  if(!data || data.length==0) {
    ElMessage({
      type: "error",
      message: `获取` + obj.view.title + `失败`,
    });
    return;
  }

  obj.jsonObj.leftOption.leftApiResp = data;

  putParamData(obj.jsonObj.leftOption.leftApiResp[0].id);
};

const getTableData = (params) => {
  params = _.merge({},params,initParam);

  return http.post<ResPage<{}>>('/boots/' + obj.view.hashkey + '/_griddata', params);
}

const handleItemClick = function (id) {
  proTableRef.value!.pageable.pageNum = 1;
  putParamData(id)
}


const putParamData = (val) => {
  eval('initParam.' + obj.jsonObj.leftOption.paramName + '=' + val);
}


const classObject = (id) => {
  return initParam.dsid == id  ? "dsSelected" : "";
}


const topBtnHandler = (btn) => {
  if(btn.isDefault){
    switch(btn.name) {
      case '__add':
        __addHandler(btn);
        break;
      case '__edit':
        __editHandler(btn);
        break;
      case '__delete':
        __deleteHandler(btn);
        break;
      case '__detail':
        __detailHandler(btn);
        break;
      case '__refresh':
        __refreshHandler(btn);
        break;
      case '__setting':
        __settingHandler(btn);
        break;
      default:
        eval( btn.name + 'Handler(btn)');
        break;
    }
  }
}
const rowBtnHandler = (btn,row) => {
  if(btn.isDefault){
    eval( btn.name + 'Handler(btn,row)');
  }
}

const __refreshHandler = (btn) => {
  proTableRef.value?.search();
}

const __settingHandler = (btn) => {
  proTableRef.value?.openColSetting();
}

const __addHandler = (btn) => {
  if(!btn.target) {
    ElMessage.warning("请配置操作目标！");
    return;
  }
  opWindow(btn,__add);
}
const __add = async (data) => {
  const resp = await http.post('/boots/' + obj.view.hashkey + '/add.do', data);
  if(resp.code == 200) {
    ElMessage.success(resp.msg);
    proTableRef.value?.search();
  }else {
    ElMessage.error(resp.msg);
  }
}

const __editHandler = async (btn,row) => {
  if(!btn.target) {
    ElMessage.warning("请配置操作目标！");
    return;
  }
  let ids;
  if(row){
    ids = [row.id]
  } else {
    if(proTableRef.value?.radio){
      ids = [...proTableRef.value?.radio]
    }else if(proTableRef.value?.selectedListIds){
      ids = proTableRef.value?.selectedListIds
    }
  }

  if(ids==null || ids.length!=1) {
    ElMessage.warning("请选择一条数据！");
    return;
  }

  const { data } = await http.get('/boots/' + btn.target + '/detail?id=' + ids[0] );

  dlgObj.value.data = data;

  opWindow(btn,__edit);
}
const __edit = async (data) => {
  data.id = dlgObj.value.data.id;
  const resp = await http.post('/boots/' + obj.view.hashkey + '/edit.do', data);
  if(resp.code == 200) {
    ElMessage.success(resp.msg);
    dlgObj.value.show = false;
    proTableRef.value?.search();
  }else {
    ElMessage.error(resp.msg);
  }
}

const __detailHandler = async (btn,row) => {
  if(!btn.target) {
    ElMessage.warning("请配置操作目标！");
    return;
  }
  let ids;
  if(row){
    ids = [row.id]
  } else {
    if(proTableRef.value?.radio){
      ids = [...proTableRef.value?.radio]
    }else if(proTableRef.value?.selectedListIds){
      ids = proTableRef.value?.selectedListIds
    }
  }

  if(ids==null || ids.length!=1) {
    ElMessage.warning("请选择一条数据！");
    return;
  }

  const { data } = await http.get('/boots/' + btn.target + '/detail?id=' + ids[0] );

  dlgObj.value.data = data;

  opWindow(btn,__detail);
}

const __detail = async (data) => {
  data.id = dlgObj.value.data.id;
  dlgObj.value.show = false;
}


const handleListener = (param) => {
  console.log(param)
  if(!dlgObj.value.callback)  return ;
  if(saveTxtList.includes(param.type)) {
    dlgObj.value.callback(param.data)
  }else if(closeTxtList.includes(param.type)){
    dlgObj.value.show = false;
  }
}

const __deleteHandler = async (btn,row) => {
  let ids;
  if(row){
    ids = [row.id]
  } else {
    if(proTableRef.value?.radio){
      ids = [...proTableRef.value?.radio]
    }else if(proTableRef.value?.selectedListIds){
      ids = proTableRef.value?.selectedListIds
    }
  }

  if(!ids || ids.length==0) {
    ElMessage.warning("请选择数据！");
    return;
  }

  ElMessageBox.confirm(`是否删除这些数据吗?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const resp = await http.post('/boots/' + obj.view.hashkey + '/delete.do', ids);
    if(resp.code == 200) {
      ElMessage.success(resp.msg);
      proTableRef.value?.search();
    }else {
      ElMessage.error(resp.msg);
    }
  })
}


</script>
<style scoped lang="scss">
  .dsSelected{
    border: 1px solid var(--el-color-primary);
  }
</style>

