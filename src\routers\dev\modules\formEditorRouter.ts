import { RouteRecordRaw } from "vue-router";
import { HOME_URL, LOGIN_URL } from "@/config";


import FormEditorView from '@/form/views/formEditor.vue'
import FormEditorConfigView from '@/form/views/formEditorConfig.vue'
import FormEditorObjListView from '@/form/views/formEditor/objList.vue'
import FormEditorObjEditView from '@/form/views/formEditor/objEdit.vue'
import FormEditorActionEditView from '@/form/views/formEditor/actionEdit.vue'
import FormEditorActionListView from '@/form/views/formEditor/actionList.vue'


export const formRouter: RouteRecordRaw[] = [
  {
    path: "/dev/formEditor",
    name: "formEditor",
    component: FormEditorView,
    meta: {
      title: "表单编辑器"
    }
  },
  {
    path: "/dev/formEditorConfig",
    name: "formEditorConfig",
    component: FormEditorConfigView,
    meta: {
      title: "属性配置器"
    }
  },
  {
    path: "/dev/formEditor/objList",
    name: "formEditorObjList",
    component: FormEditorObjListView,
    meta: {
      title: "表单列表"
    }
  },
  {
    path: "/dev/formEditor/objEdit/:objid?",
    name: "formEditorObjEdit",
    component: FormEditorObjEditView,
    meta: {
      title: "表单编辑"
    }
  },
];
