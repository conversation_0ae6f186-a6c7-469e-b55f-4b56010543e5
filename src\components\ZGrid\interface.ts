export interface Props {
  leftOption: {
    title: String,
    showLeft: <PERSON><PERSON><PERSON>,
    showSearch: <PERSON><PERSON><PERSON>,
    leftWidth: Number,
    leftApi: String,
    paramName: String,
  }
  tableOption: {
    columns: [],
    tableApi: String,
    pageable: {
      // 当前页数
      pageNum: Number,
      // 每页显示条数
      pageSize: Number,
      // 总条数
      total: Number,
      pageSizes : [],
    },
    rowKey: String,
  }
}

