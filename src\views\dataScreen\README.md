# 🎨 数据大屏设计器

一个功能强大的拖拽式数据大屏开发面板，让你可以快速创建专业的数据可视化大屏。

## ✨ 核心功能

### 🎯 设计器界面
- **左侧组件库**: 包含图表组件、数据组件、装饰组件
- **中间画布区域**: 支持拖拽、缩放、网格布局
- **右侧属性面板**: 实时配置组件属性、样式、数据源
- **顶部工具栏**: 保存、导出、预览等操作

### 📊 组件类型

#### 图表组件
- 实时访问图 (折线图)
- 饼图 (男女比例等)
- 柱状图 (年龄分布等)
- 地图 (中国地图)
- 年度对比图
- 平台统计图
- 热门排行图

#### 数据组件
- 数据卡片 (KPI指标展示)
- 数据表格 (排行榜等)
- 数据列表

#### 装饰组件
- 文本组件 (标题、描述等)
- 边框装饰
- 背景元素

## 🚀 使用指南

### 1. 添加组件
1. 从左侧组件库选择需要的组件
2. 拖拽到中间画布区域
3. 组件会自动添加到画布上

### 2. 配置组件
1. 点击画布上的组件进行选择
2. 在右侧属性面板中配置：
   - **基础属性**: 组件名称、尺寸等
   - **样式配置**: 背景色、边框、圆角等
   - **数据配置**: 数据源、API接口等

### 3. 画布操作
- **缩放**: 使用画布工具栏的缩放按钮
- **重置**: 清空画布所有组件
- **拖拽**: 直接拖拽组件调整位置
- **调整大小**: 拖拽组件边角调整尺寸

### 4. 保存和导出
- **保存**: 将设计保存到本地存储
- **导出**: 导出为JSON配置文件
- **预览**: 切换到全屏预览模式

## 🎨 数据配置

### 静态数据
```json
{
  "value": 1234,
  "unit": "万",
  "label": "总访问量",
  "trendValue": "+12.5%"
}
```

### API数据源
```javascript
// 配置API地址
apiUrl: "https://api.example.com/data"

// 数据会自动获取并更新组件
```

### 实时数据
支持WebSocket等实时数据更新机制

## 🎯 组件详细说明

### 数据卡片组件
- 支持数值格式化 (万、k等单位)
- 趋势指标显示 (上升、下降、持平)
- 自定义样式和颜色
- 响应式布局

### 数据表格组件
- 排行榜样式
- 趋势图标
- 自定义列配置
- 滚动支持

### 文本组件
- 多种文本类型 (标题、副标题、段落等)
- 丰富的样式配置
- 字体、颜色、对齐方式等

## 🔧 技术栈

- **Vue 3** + **TypeScript** - 现代化前端框架
- **Element Plus** - UI组件库
- **ECharts** - 图表库
- **vue-grid-layout** - 网格布局
- **SCSS** - 样式预处理器

## 📱 响应式支持

设计器支持多种屏幕尺寸：
- 大屏显示 (1920x1080)
- 桌面显示 (1366x768)
- 平板显示 (768x1024)

## 🎨 主题定制

支持自定义主题色彩：
- 主色调配置
- 背景渐变
- 组件边框色
- 文字颜色等

## 📝 开发说明

### 添加新组件
1. 在 `components/` 目录下创建新组件
2. 在主文件中注册组件
3. 添加到组件库配置中

### 扩展功能
- 数据源适配器
- 自定义图表类型
- 动画效果
- 交互功能

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License
