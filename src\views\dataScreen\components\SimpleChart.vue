<template>
  <div class="simple-chart" :style="chartStyle">
    <div class="chart-header" v-if="config.showTitle">
      <h3 class="chart-title">{{ config.title || '简单图表' }}</h3>
    </div>
    <div class="chart-content" ref="chartRef">
      <!-- 这里可以放置实际的图表内容 -->
      <div class="demo-content">
        <div class="demo-bar" v-for="(item, index) in demoData" :key="index" :style="{ height: item.value + '%' }">
          <span class="bar-label">{{ item.name }}</span>
          <span class="bar-value">{{ item.value }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

interface Props {
  config?: {
    title?: string;
    showTitle?: boolean;
    backgroundColor?: string;
    textColor?: string;
    data?: any[];
  };
  style?: any;
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({
    title: '简单图表',
    showTitle: true,
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    textColor: '#ffffff',
    data: []
  })
});

const chartRef = ref<HTMLElement | null>(null);

const chartStyle = computed(() => ({
  backgroundColor: props.config?.backgroundColor || 'rgba(64, 158, 255, 0.1)',
  color: props.config?.textColor || '#ffffff',
  ...props.style
}));

// 演示数据
const demoData = computed(() => {
  if (props.config?.data && props.config.data.length > 0) {
    return props.config.data;
  }
  return [
    { name: 'A', value: 80 },
    { name: 'B', value: 65 },
    { name: 'C', value: 90 },
    { name: 'D', value: 45 },
    { name: 'E', value: 75 }
  ];
});
</script>

<style lang="scss" scoped>
.simple-chart {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  padding: 15px;
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid rgba(64, 158, 255, 0.3);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;

  .chart-header {
    margin-bottom: 15px;

    .chart-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: inherit;
    }
  }

  .chart-content {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: center;

    .demo-content {
      display: flex;
      align-items: flex-end;
      justify-content: space-around;
      width: 100%;
      height: 100%;
      gap: 8px;

      .demo-bar {
        flex: 1;
        background: linear-gradient(180deg, #409eff 0%, #67c23a 100%);
        border-radius: 4px 4px 0 0;
        position: relative;
        min-height: 20px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        .bar-label {
          position: absolute;
          bottom: -20px;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
        }

        .bar-value {
          position: absolute;
          top: -25px;
          font-size: 11px;
          color: #fff;
          background: rgba(0, 0, 0, 0.6);
          padding: 2px 6px;
          border-radius: 4px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover .bar-value {
          opacity: 1;
        }
      }
    }
  }
}
</style>
