<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      row-key="id"
      :indent="20"
      stripe
      :columns="columns"
      :tool-button="false"
      :request-api="getMainList"
      @row-dblclick="rowDblClick"
      :request-auto="false"
      :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
    >
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="icons.Check" @click="selectRow(scope.row)" >编辑该对象</el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="ts" name="MainSelector">
import {defineEmits, onMounted, reactive, ref} from "vue";
import { useRouter } from "vue-router";
import {
  ElNotification,
  UploadRequestOptions,
  UploadRawFile,
  FormRules,
  FormInstance,
  ElMessage,
  ElMessageBox
} from "element-plus";
import {Ds, ResPage} from "@/api/interface";
import { getMainList} from "@/api/dev/main";
import http from "@/api";
import {PORT1} from "@/api/config/servicePort";
import ProTable from "@/components/ProTable/index.vue";
import {ProTableInstance} from "@/components/ProTable/interface";
import * as icons from "@element-plus/icons-vue";
import ZeroTable from "@/components/ZeroTable/index.vue";


// ProTable 实例
const proTable = ref<ProTableInstance>();

onMounted(() => {

  proTable.value!.pageable.pageSizes = [15,30,50];
  proTable.value!.pageable.pageSize = 15;
  proTable.value!.search();
});





const columns = reactive([
  { type: "index", label: "#", width: 80 },
  { prop: "type", label: "业务类型",sortable:true,
    tag: true,
    enum: [
      {label: "BI 报表",value: "1", tagType:"success"},
      {label: "信息管理",value: "2", tagType:"primary"},
    ],
    search: { el: "select" },
  },
  { prop: "name", label: "名称",sortable:true, search: { el: "input" }  },
  { prop: "showname", label: "显示名",sortable:true, search: { el: "input" }  },
  { prop: "dbname", label: "数据源",sortable:true },
  { prop: "tablename", label: "表名",sortable:true },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]);


const emit = defineEmits(['selected'])

const rowDblClick = (row) => {
  selectRow(row);
}

const router = useRouter();

const selectRow = (row) => {
  console.log("selectRow")
  console.log(`/dev/main/editor/${row.id}`)
  router.push(`/dev/main/editor/${row.id}`);
  // console.log(row)
  // emit('selected',row)
  // dialogVisible.value = false;
}


defineExpose({
  // openDialog
});
</script>
