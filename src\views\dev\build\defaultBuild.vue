<template>
  <div class="main-box">
    <div :style="{'width': 'calc(100% - ' + width + 'px)'}">
      <KeepAlive>
        <component
          :is="obj.comment.viewComment"
          :key="key"
        ></component>
      </KeepAlive>
    </div>
    <div class="card filter" :style="{'width': width + 'px'}">
      <div style="height:60px">
        <el-text size="large" type="primary">{{obj.view.type}} - {{obj.view.title}}</el-text>
        <el-button-group style="display: inline;float: right">
          <el-button type="success" size="small" round title="保存" @click="saveHandler" >保存</el-button>
          <el-button type="primary" size="small" round title="预览" @click="openHandler" >预览</el-button>
        </el-button-group>
      </div>
      <el-form label-width="80px"
               label-position="left"
               size="small" style="height:calc(100% - 60px);overflow-y: auto;overflow-x: hidden;">
        <component
          :is="obj.comment.optionComment"
        ></component>
      </el-form>
    </div>

    <ZDialog ref="dlgRef"></ZDialog>

  </div>
</template>

<script setup lang="ts" name="build">
import { provide, reactive, ref} from "vue";
import * as icons from '@element-plus/icons-vue'
import  ZDialog  from '@/components/ZDialog/index.vue'
import { saveView, getView }  from "@/api/dev/view"
import {ElMessage} from "element-plus";


const dlgRef = ref({});
const props = defineProps({
  id: Number,
  mainid: Number,
  hashkey: String,
  type : String,
  title: String,
  fit: String,
  width: Number,
  json: String,
})

const obj = reactive({
  comment: {},
  view: {},
  jsonObj: {},
})

Object.assign(obj.view,props) ;
obj.jsonObj = props.json?JSON.parse(props.json):{} ;
obj.comment.name = props.type.substr(0,1).toUpperCase() + props.type.substr(1);
obj.comment.optionComment = obj.comment.name + 'Option';
obj.comment.viewComment = obj.comment.name;

provide('obj',obj);

const width = ref(props.width || 335);

const key = ref(1);
const saveHandler = async () => {
  let data = {};
  Object.assign(data,obj.view);
  data.json = JSON.stringify(obj.jsonObj);

  const resp = await saveView(data);

  if(resp.code === 200) {
    ElMessage.success({ message: resp.msg });
    key.value++;
  }else {
    ElMessage.error({ message: resp.msg });
  }
}

const openHandler = () => {
  open('#/view/' + obj.view.hashkey )
}

</script>

