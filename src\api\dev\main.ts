import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";



// 获取数据对象列表
export const getMainList = (params) => {
  return http.post(PORT1 + `/../dev/main/getMainList`, params);
};

// 获取数据对象列表
export const getAllMainByType = (type) => {
  return http.get(PORT1 + `/../dev/main/getAllMainByType?type=` + type);
};


// 获取数据对象详情
export const getMainDetail = (id) => {
  return http.get(PORT1 + `/../dev/main/getMainDetail?id=` + id);
};

// 获取数据对象详情
export const getMain = (id) => {
  return http.get(PORT1 + `/../dev/main/getMain?id=` + id);
};


// 保存数据对象
export const save = (params) => {
  return http.post(PORT1 + `/../dev/main/save` ,params);
};

// 删除数据对象
export const del = (id) => {
  return http.get(PORT1 + `/../dev/main/del?id=` + id );
};

// 删除数据对象
export const rebuild = (id) => {
  return http.get(PORT1 + `/../dev/main/rebuild?id=` + id );
};

// 删除数据对象
export const clone = (id) => {
  return http.get(PORT1 + `/../dev/main/clone?id=` + id );
};





