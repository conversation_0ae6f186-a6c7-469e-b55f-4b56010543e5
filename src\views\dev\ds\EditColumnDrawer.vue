<template>
  <!-- 列设置 -->
  <el-drawer v-model="drawerVisible" :title="`${op}字段`" size="450px">
    <el-form
      ref="formRef"
      :model="obj"
      status-icon
      :rules="columnRules"
      :hide-required-asterisk="false"
      label-width="120px"
    >
      <template v-if="obj.type=='mysql' || obj.type=='mariadb'">
        <el-form-item label="字段名称 :" prop="dbcolumn" >
          <el-input v-model="obj.dbcolumn"></el-input>
        </el-form-item>
        <el-form-item label="字段类型 :" prop="dbtype" >
          <el-select v-model="obj.dbtype">
            <el-option label="int" value="int"></el-option>
            <el-option label="varchar" value="varchar"></el-option>
            <el-option label="timestamp" value="timestamp"></el-option>
            <el-option label="decimal" value="decimal"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="字段长度 :"  >
          <el-input v-model="obj.length"></el-input>
        </el-form-item>
        <el-form-item label="字段描述 :"  >
          <el-input v-model="obj.showname"></el-input>
        </el-form-item>
      </template>
      <el-form-item label=""  >
        <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts" name="EditColumnDrawer">
import {defineEmits, reactive, ref} from "vue";
import { ColumnProps } from "@/components/ProTable/interface";
import {ElMessage, FormInstance, FormRules} from "element-plus";
import {saveColumn} from "@/api/dev/ds";
import {Ds} from "@/api/interface";


const drawerVisible = ref<boolean>(false);

const obj = ref();

let op = ref(null);

let oldparam ;

const openDrawer = (param) => {
  oldparam = {};
  Object.assign(oldparam,param);
  obj.value = param;
  if(obj.value.dbcolumn)obj.value.olddbcolumn = obj.value.dbcolumn;
  op.value = obj.value.dbcolumn?'修改':'新增';
  drawerVisible.value = true;
};

const emit = defineEmits(['save'])

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      console.log(oldparam)
      const param = {
        dsid: oldparam.dsid,
        tablename: oldparam.tablename,
        columnname: oldparam.dbcolumn?oldparam.dbcolumn:null,
        oldcolumnname: oldparam.dbcolumn?oldparam.dbcolumn:null,
        newcolumnname: obj.value.dbcolumn,
        oldcolumntype: oldparam.dbtype?oldparam.dbtype:null,
        newcolumntype: obj.value.dbtype,
        oldcolumnlength: oldparam.length?oldparam.length:null,
        newcolumnlength: obj.value.length,
        oldcolumncomment: oldparam.showname?oldparam.showname:null,
        newcolumncomment: obj.value.showname,
      }
      const { data } = await saveColumn(param) ;

      ElMessage.success({ message: `操作成功！` });

      drawerVisible.value = false;
      emit('save');

    } catch (error) {
      console.log(error);
    }
  });
}

const formRef = ref();

const columnRules = reactive({
  dbcolumn: [{ required: true, message: "请输入字段名称" }],
  dbtype: [{ required: true, message: "请选择字段类型" }],
});

defineExpose({
  openDrawer
});
</script>

<style scoped lang="scss">
  .cursor-move {
    cursor: move;
  }
</style>
