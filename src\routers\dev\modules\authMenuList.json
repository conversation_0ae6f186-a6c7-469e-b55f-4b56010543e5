{"code": 200, "data": [{"path": "/index", "name": "home", "component": "/dev/shouye", "meta": {"icon": "HomeFilled", "title": "首页", "isLink": "", "isHide": false, "isFull": false, "isAffix": true, "isKeepAlive": true}}, {"path": "/dev/ds", "name": "ds", "component": "/dev/ds/index", "meta": {"icon": "Coin", "title": "数据源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/main", "name": "MainSelector", "component": "/dev/main/MainSelector", "meta": {"icon": "Grid", "title": "数据对象", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/dev/main/editor/:id", "name": "maineditor", "component": "/dev/main/index", "meta": {"icon": "Grid", "title": "数据对象设计器", "activeMenu": "/dev/main", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/dev/xscreen", "name": "xscreen", "redirect": "/dev/link/%2Favue-data%2F/%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F", "meta": {"icon": "Monitor", "title": "数据大屏", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/app", "name": "app", "component": "/dev/app/index", "meta": {"icon": "Apple", "title": "应用管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/file", "name": "file", "component": "/dev/file/index", "meta": {"icon": "Files", "title": "资源文件", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true, "expandChildren": true}}, {"path": "/dev/enum", "name": "enum", "component": "/dev/enum/index", "meta": {"icon": "List", "title": "枚举字典", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/api", "name": "api", "component": "/dev/api/index", "meta": {"icon": "Link", "title": "API 接口", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/queryctl", "name": "queryctl", "component": "/dev/queryctl/index", "meta": {"icon": "<PERSON><PERSON>", "title": "自定义组件", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/menuview/:key", "name": "menuview", "component": "/app/Preview", "meta": {"icon": "<PERSON><PERSON>", "title": "", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/link/:key/:title", "name": "link", "component": "/link/index", "meta": {"icon": "<PERSON><PERSON>", "title": "外链", "isLink": "", "isRight": true, "isHide": true, "isFull": false, "isAffix": false, "isKeepAlive": true}}], "msg": "成功"}