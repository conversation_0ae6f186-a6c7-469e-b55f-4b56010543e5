<template>
  <el-dialog v-model="dialogVisible" title="编辑按钮" width="80%" draggable>
    <el-form
      ref="ruleFormRef"
      label-suffix=":"
      label-position="right"
      label-width="150"
      :model="obj"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="按钮文本" prop="label">
            <el-input class="mx-1" v-model="obj.label" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="obj.type" clearable >
              <el-option label="primary" value="primary"></el-option>
              <el-option label="success" value="success"></el-option>
              <el-option label="warning" value="warning"></el-option>
              <el-option label="danger" value="danger"></el-option>
              <el-option label="info" value="info"></el-option>
              <el-option label="text" value="text"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="朴素按钮" prop="plain">
            <el-switch v-model="obj.plain"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="文字按钮" prop="text">
            <el-switch v-model="obj.text"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="显示背景颜色" prop="bg">
            <el-switch v-model="obj.bg"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="链接按钮" prop="link">
            <el-switch v-model="obj.link"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="圆角按钮" prop="round">
            <el-switch v-model="obj.round"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="圆形按钮" prop="circle">
            <el-switch v-model="obj.circle"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="暗黑模式" prop="dark">
            <el-switch v-model="obj.dark"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="大小" prop="size">
            <el-radio-group v-model="obj.size" >
              <el-radio-button key="large" label="large"></el-radio-button>
              <el-radio-button key="default" label="default"></el-radio-button>
              <el-radio-button key="small" label="small"></el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="原生类型" prop="nativeType">
            <el-select v-model="obj.nativeType">
              <el-option label="button" value="button"></el-option>
              <el-option label="submit" value="submit"></el-option>
              <el-option label="reset" value="reset"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="自定义颜色" prop="color">
            <el-color-picker v-model="obj.color" show-alpha />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图标" prop="icon">
            <SelectIcon v-model:icon-value="obj.icon" clearable title="asdf"></SelectIcon>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="操作行为" prop="action">
            <el-select v-model="obj.action" >
              <el-option value="dialog" label="弹窗"></el-option>
              <el-option value="draw" label="抽屉"></el-option>
              <el-option value="label" label="标签页打开"></el-option>
              <el-option value="window" label="新窗口打开"></el-option>
              <el-option value="self" label="当前页面替换"></el-option>
              <el-option value="api" label="API接口调用"></el-option>
              <el-option value="js" label="JS执行"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操作目标" prop="target">
            <el-input v-model="obj.target"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12" v-if="obj.action=='dialog' || obj.action == 'draw'">
          <el-form-item label="目标大小" prop="targetSize">
            <el-input v-model="obj.targetSize"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="参数" prop="target">
            <el-input v-model="obj.target"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12" v-if="obj.isTop">
          <el-form-item label="是否选择数据" prop="selectMode">
            <el-switch v-model="obj.selectMode"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="TopBtnDialog">
import {ref, reactive, withDefaults} from "vue";
import { ElMessage, FormInstance } from "element-plus";

// import iconSon from "@/components/iconSon.vue"
import SelectIcon from "@/components/SelectIcon/index.vue";


const dialogVisible = ref(false);

const obj = ref({});

let btn ;

const openDialog = (btnInfo) => {
  btn = btnInfo;
  obj.value = {};
  Object.assign(obj.value,btnInfo)

  dialogVisible.value = true;
};

defineExpose({ openDialog });

const handleSubmit = () => {
  Object.assign(btn,obj.value)

  dialogVisible.value = false;
}

</script>
<style >
  /*.el-form-item__label{*/
  /*  text-align: right;*/
  /*}*/
  .el-form--label-left .el-form-item__label {
    text-align: left !important;
    /* justify-content: flex-end !important;  */
  }
</style>
