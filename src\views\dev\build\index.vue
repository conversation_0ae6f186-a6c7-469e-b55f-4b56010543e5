<template>
  <Component :is="comp" v-bind="obj" style></Component>
</template>

<script setup lang="ts" name="useProTableDetail">
import {onMounted, reactive, ref, watch, shallowRef} from "vue";
import { useRoute } from "vue-router";
import { getView } from "@/api/dev/view";

const route = useRoute();

const comp = shallowRef();
const obj = reactive({});

onMounted(() => {
  load();
});



const load = async () => {

  const { data } = await getView(route.params.key);

  Object.assign(obj,data);

  let c;
  switch (obj.type) {
    case "bi":
      c = await import(`@/views/dashboard/editor/index.vue`);
      comp.value = c.default;
      break;
    case "add":
    case "detail":
    case "edit":
    case "query":
      c = await import(`@/views/dev/FormDesigner.vue`);
      comp.value = c.default;
      break;
    default:
      c = await import(`./defaultBuild.vue`);
      comp.value = c.default;
      break;
  }
}



</script>
