<template>
  <div class="main-box">
    <div class="card filter">
      <h4 class="title sle">数据源
        <el-button style="float:right;margin-left:5px" circle :icon="icons.Refresh" @click="getDs" title="刷新" />
        <el-button style="float:right" circle :icon="icons.Plus"  @click="openDsDlg()" title="新增数据源" />
      </h4>
      <el-input v-model="filterText" placeholder="输入关键字进行过滤" clearable />
      <el-scrollbar :style="{ height: `calc(100% - 107px)`  }">
        <template v-for="ds in dsData">
        <div v-if="showItem(ds)"
             :id="ds.id"
             v-on:click="handleDsClick(ds)"
             class="dsObj"
             :class="classObject(ds.id)"
          >
          <img :src="`/src/assets/db/${ds.type}.png`" style="width: 60px; display: inline-block;" alt="">
          <span style="width: 110px;height:60px; display: inline-block;margin-left: 5px;position: relative">
            <div>{{ ds.name }}</div>
            <div>{{ ds.ip }}</div>
            <el-button-group style="display:none;position:absolute;bottom:0;right:0">
              <el-button type="primary" :icon="icons.Edit" size="small"  @click.stop="openDsDlg(ds)" />
              <el-button type="warning" :icon="icons.Delete" size="small" @click.stop="del(ds.id)" />
            </el-button-group>
          </span>
        </div>
        </template>
      </el-scrollbar>
    </div>

    <div class="table-box">
      <ProTable
        ref="proTable"
        row-key="id"
        title="数据库表"
        :indent="20"
        stripe
        :columns="columns"
        :request-api="getDsTables"
        :requestError="getDataErr"
        :request-auto="false"
        :init-param="initParam"
        :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader>
          <el-button type="primary" :icon="icons.CirclePlus" @click="openCreateTableDrawer()"
                     v-if="['mysql','mariadb'].includes(initParam.type)">建表</el-button>
          <el-button type="success" :icon="icons.CirclePlus" @click="openChatGPTDialog">ChatGPT</el-button>
          <el-dropdown placement="bottom-start" style="margin-left:12px">
            <el-button type="success" plain :icon="icons.Upload"> 上传Excel </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-upload
                  class="upload-demo"
                  :action="uploadUrl"
                  :auto-upload="true"
                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                  :data="{dsid: initParam.dsid, makeType:makeType}"
                  :headers="headObj"
                  :with-credentials="true"
                  :show-file-list="false"
                  :on-success="uploadSuccess"
                >
                  <el-dropdown-item @click="makeType=''">上传并建表</el-dropdown-item>
                  <el-dropdown-item @click="makeType='MIS'">上传并创建MIS</el-dropdown-item>
                  <el-dropdown-item @click="makeType='BI'">上传并创建BI</el-dropdown-item>
                </el-upload>

              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button type="primary" link :icon="icons.View" @click="openTableInfoDlg(scope.row)">查看</el-button>
          <el-button type="primary" link :icon="icons.Grid" @click="createMis(scope.row)">创建MIS</el-button>
          <el-button type="primary" link :icon="icons.Histogram" @click="createBI(scope.row)">创建BI</el-button>
        </template>
      </ProTable>
    </div>
    <EditDsDialog ref="dsDlgRef" @save="getDs" />
    <TableInfoDialog ref="tableInfoRef" @save="proTable.search()"></TableInfoDialog>
    <CreateTableDrawer ref="createTableRef" @save="proTable.search()"></CreateTableDrawer>
    <ChatGPTDialog ref="chatGPTRef" @save="proTable.search()"></ChatGPTDialog>
  </div>
</template>

<script setup lang="tsx" name="dsIndex">
import {onMounted, reactive, ref, watch} from "vue";
import {ElMessage, ElMessageBox, ElNotification} from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import EditDsDialog from "./EditDsDialog.vue";
import TableInfoDialog from "./TableInfoDialog.vue";
import CreateTableDrawer from "./CreateTableDrawer.vue";
import ChatGPTDialog from "./ChatGPTDialog.vue";
import * as icons from "@element-plus/icons-vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import {getDsList, getDsTables,  delDs, makeMIS, makeBI, uploadMake} from "@/api/dev/ds";
import {useUserStore} from "@/stores/modules/user";
import {useRouter} from "vue-router";

// ProTable 实例
const proTable = ref<ProTableInstance>();
console.log("proTable.value")
console.log(proTable.value)

const makeType = ref("")
const headObj = ref();

const router = useRouter();

const uploadUrl = import.meta.env.VITE_API_URL + "/dev/datasource/_upload"

onMounted(() => {
  getDs();


  console.log("proTable.value1")
  console.log(proTable.value)

  proTable.value!.pageable.pageSize = 15;
  proTable.value!.pageable.pageSizes = [15,30,50];


  const userStore = useUserStore();

  headObj.value = new Headers({
    _zreport_dev_token : userStore._zreport_dev_token
  });

});

const dsData = ref<{ [id: string]: any }[]>([]);


const filterText = ref("");

const showItem = (ds) => {
  if(filterText.value == "") return true;

  if(ds.name!=null && ds.name.indexOf(filterText.value)>=0) return true;
  if(ds.ip!=null && ds.ip.indexOf(filterText.value)>=0) return true;
  return false;
}




// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ dsid: "" });

// 获取 treeFilter 数据
// 当 proTable 的 requestAuto 属性为 false，不会自动请求表格数据，等待 treeFilter 数据回来之后，更改 initParam.dsid 的值，才会触发请求 proTable 数据

const getDs = async () => {
  const { data } = await getDsList();

  dsData.value = data;
  if(!data || data.length===0) return;
  Object.assign(initParam,{
    dsid: dsData.value[0].id,
    type: dsData.value[0].type,
  })
};

const getDataErr = () => {
  var obj = proTable.value?.tableData;
  obj.splice(0, obj.length);

  obj = proTable.value?.pageable;
  obj.total = 0;
}

// 表格配置项
const columns = reactive([
  { type: "index", label: "#", width: 80 },
  // {  search: { el: "input", label: "表名", key: 'tablename' } },
  { prop: "tablename", label: "表名",sortable:true, search: { el: "input" } },
  { prop: "tablecomment", label: "表注释",sortable:true,search: { el: "input" } },
  { prop: "operation", label: "操作", width: 300, fixed: "right" }
]);

const dsDlgRef = ref<InstanceType<typeof EditDs> | null>(null);

const openDsDlg = (params) => {
  dsDlgRef.value?.acceptParams(params);
};

const tableInfoRef = ref();


const openTableInfoDlg = (param) => {
  Object.assign(param,{
    dsid: initParam.dsid,
    type: initParam.type,
  })

  tableInfoRef.value?.openDialog(param);
}

const createTableRef = ref();

const openCreateTableDrawer = () => {
  createTableRef.value?.openDrawer({dsid:initParam.dsid});
}

const chatGPTRef = ref();

const openChatGPTDialog = () => {
  chatGPTRef.value?.openDialog(initParam.dsid);
}

const handleDsClick = (ds) => {
  proTable.value!.pageable.pageNum = 1;
  Object.assign(initParam, {
    dsid: ds.id,
    type: ds.type,
  });
}

const classObject = (id) => {
  return initParam.dsid == id  ? "dsSelected" : "";
}

const del = async (id) => {
  ElMessageBox.confirm(`是否删除该数据源?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const res = await delDs(id);
    ElMessage({
      type: "success",
      message: `删除数据源成功!`
    });
    getDs()
  }).catch(()=>{});
}

const createMis = async (param) => {
  const resp = await makeMIS(initParam.dsid,param.tablename);

  if(resp.code === 200) {
    ElMessageBox.confirm("上传成功，是否跳转?", "上传成功", {
      confirmButtonText: "跳转到新创建数据对象",
      cancelButtonText: "取消",
      type: "success"
    }).then(() => {
      router.push("/dev/main/editor/" + resp.data)
    });
  }
}

const createBI = async (param) => {
  const resp = await makeBI(initParam.dsid,param.tablename);

  if(resp.code === 200) {
    ElMessageBox.confirm("上传成功，是否跳转?", "上传成功", {
      confirmButtonText: "跳转到新创建数据对象",
      cancelButtonText: "取消",
      type: "success"
    }).then(() => {
      router.push("/dev/main/editor/" + resp.data)
    });
  }
}


const uploadSuccess =  (
  resp,
  file
) => {
  if(resp.data) {
    ElMessageBox.confirm("上传成功，是否跳转?", "上传成功", {
      confirmButtonText: "跳转到新创建数据对象",
      cancelButtonText: "取消",
      type: "success"
    }).then(() => {
      router.push("/dev/main/editor/" + resp.data)
    });
  }else {
    ElMessage.success("上传成功");
  }

}

</script>
<style scoped lang="scss">
  .dsObj {
    border: 1px solid rgba(0,0,0,0);
    height: 60px;
    font-size: 14px;
    margin: 8px 2px;
    display: flex;
  }
  .dsObj:hover{
    border-color: var(--el-color-success);
  }
  .dsObj:hover>span>div.el-button-group{
    display:block !important;
  }
  .dsSelected{
    border-color: var(--el-color-primary);
  }
</style>

