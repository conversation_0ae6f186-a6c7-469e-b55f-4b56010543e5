import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 获取数据源列表
export const getAppList = () => {
  return http.get(PORT1 + `/../dev/app/list`);
};

export const getApp = (id) => {
  return http.get(PORT1 + `/../dev/app/info?id=` + id);
};


export const saveApp = (params) => {
  return http.post(PORT1 + `/../dev/app/save`, params);
};

export const delApp = (id: number) => {
  return http.get(PORT1 + `/../dev/app/del?id=` + id);
};

export const getAppMenu = (appid: number) => {
  return http.get(PORT1 + `/../dev/${appid}/allmenu` );
};

export const saveAppMenu = (appid: number,menuList) => {
  return http.post(PORT1 + `/../dev/${appid}/saveallmenu`, menuList );
};




