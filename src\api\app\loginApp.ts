import { Login } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import authMenuList from "@/routers/front/modules/authMenuList.json";
import authButtonList from "@/routers/front/modules/authButtonList.json";
import http from "@/api";

/**
 * @name 登录模块
 */
// 用户登录
export const loginApi = (params: Login.ReqLoginForm,appname: string) => {
  return http.post<Login.ResLogin>(PORT1 + `/../${appname}/loginPost`, params, { loading: false }); // 正常 post json 请求  ==>  application/json
};

// 获取菜单列表
export const getAppAuthMenuListApi = (appname: string) => {
  return http.get<Menu.MenuOptions[]>(PORT1 + `/../${appname}/menuList`);
  // 如果想让菜单变为本地数据，注释上一行代码，并引入本地 authMenuList.json 数据
  // return authMenuList;
};

// 获取按钮权限
export const getAuthButtonListApi = () => {
  // return http.get<Login.ResAuthButtons>(PORT1 + `/auth/buttons`, {}, { loading: false });
  // 如果想让按钮权限变为本地数据，注释上一行代码，并引入本地 authButtonList.json 数据
  return authButtonList;
};

// 用户退出登录
export const logoutApi = () => {
  return http.post(PORT1 + `/logout`);
};


export const getAppInfo = (appname: string) => {
  return http.get(PORT1 + `/../${appname}/appinfo`);
  // 如果想让菜单变为本地数据，注释上一行代码，并引入本地 authMenuList.json 数据
  // return authMenuList;
};

