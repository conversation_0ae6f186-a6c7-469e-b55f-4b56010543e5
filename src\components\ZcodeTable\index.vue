<template>
  <div class="main-box">
    <div class="card filter" v-if="obj.jsonObj.leftOption.showLeft" :style="{'width': obj.jsonObj.leftOption.leftWidth + 'px'}" >
      <h4 class="title sle" v-if="obj.jsonObj.leftOption.showTitle">{{obj.view.title}}</h4>
      <el-input v-model="filterText" placeholder="输入关键字进行过滤" clearable v-if="obj.jsonObj.leftOption.showSearch" />
      <el-scrollbar :style="{ height: `calc(100% - 97px)`  }">
        <template v-for="item in obj.jsonObj.leftOption.leftApiResp">
          <div v-if="showItem(item)"
               :id="item.id"
               v-on:click="handleItemClick(item.id)"
               :class="classObject(item.id)"
               style="height: 80px;font-size: 14px;margin: 8px 2px">
            <img :src="`/src/assets/db/${item.type}.png`" style="width: 60px; display: inline-block;" alt="">
            <span style="width: 110px;height:60px; display: inline-block;;margin-left: 5px">
              <div>{{ item.name }}</div>
              <div>{{ item.ip }}</div>
              <el-button-group>
                <el-button  plain :icon="icons.Edit" size="small"  @click.stop="openDsDlg(item)" />
                <el-button type="warning" plain :icon="icons.Delete" size="small" @click.stop="del(item.id)" />
              </el-button-group>
            </span>
          </div>
        </template>
      </el-scrollbar>
    </div>

    <div class="table-box" :style="{ 'width': (obj.jsonObj.leftOption.showLeft? 'calc(100% - ' + obj.jsonObj.leftOption.leftWidth + 'px)': '100%') }">
      <ProTable
        ref="proTable"
        :row-key="obj.jsonObj.tableOption.rowKey"
        :indent="20"
        :columns="computedColumns"
        :request-api="getTableData"
        :request-auto="!obj.jsonObj.leftOption.showLeft"
        :init-param="initParam"
        :title="obj.view.title"
        :pagination="obj.jsonObj.tableOption.pagination"
        :border="obj.jsonObj.tableOption.border"
        :toolButton="obj.jsonObj.tableOption.toolButton"
        @updateData="updateData"
        :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader>
          <el-button v-for="btn in obj.jsonObj.tableOption.topBtns" v-bind="btn" @click="btn.clickFunction">{{btn.label}}</el-button>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button v-for="btn in obj.jsonObj.tableOption.rowBtns" v-bind="btn" @click="btn.clickFunction(scope.row)" >{{btn.label}}</el-button>
        </template>
      </ProTable>
    </div>
    <ZDialog ref="dlgRef"></ZDialog>

  </div>
</template>

<script setup lang="tsx" name="ZcodeTable">
import {onMounted, reactive, ref, watch, inject, computed} from "vue";
import {Ds, ResPage, User} from "@/api/interface";
import { genderType } from "@/utils/dict";
import { useHandleData } from "@/hooks/useHandleData";
import {ElMessage, ElMessageBox, ElNotification} from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
// import TreeFilter from "@/components/TreeFilter/index.js";
// import NodeDsFilter from "@/components/NodeDsFilter/index.js";
import ImportExcel from "@/components/ImportExcel/index.vue";
import UserDrawer from "@/views/proTable/components/UserDrawer.vue";
import * as icons from "@element-plus/icons-vue";
// import { CirclePlus, Delete, EditPen, View, Upload, Grid, Histogram } from "@element-plus/icons-vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import {getUserTreeList, deleteUser, editUser, addUser, getUserStatus, exportUserInfo, BatchAddUser} from "@/api/modules/user";
import {getDsList, getDsTables, saveDs, delDs} from "@/api/dev/ds";
import http from "@/api";
import { Props } from "./interface"
import {PORT1} from "@/api/config/servicePort";
import  ZDialog  from '@/components/ZDialog/index.vue'

let dlgRef = ref({});


const obj = inject('obj',reactive({
  view: {},
  jsonObj: {},
}));

const openDlg = (param) => {
  dlgRef.value?.openDlg(param);
}

onMounted(() => {
  getLeftData();
  proTable.value!.pageable = obj.jsonObj.tableOption.pageable;
});

// 一个计算属性 ref
const computedColumns = computed(() => {
  return [{
    type: obj.jsonObj.tableOption.pkType,
    width: 80,
  }, ...obj.jsonObj.tableOption.columns];
})



const updateData = (data) => {
  obj.jsonObj.tableOption.tableApiResp = data
}

const filterText = ref("");

const showItem = (item) => {
  if(filterText.value == "") return true;

  if(item.name!=null && item.name.indexOf(filterText.value)>=0) return true;
  if(item.ip!=null && item.ip.indexOf(filterText.value)>=0) return true;
  return false;
}

// ProTable 实例
const proTable = ref<ProTableInstance>();


// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// 获取 treeFilter 数据
// 当 proTable 的 requestAuto 属性为 false，不会自动请求表格数据，等待 treeFilter 数据回来之后，更改 initParam.dsid 的值，才会触发请求 proTable 数据

const getLeftData = async () => {
  const { data } = await http.get(obj.jsonObj.leftOption.leftApi, {}, { cancel: false });

  if(!data || data.length==0) {
    ElMessage({
      type: "error",
      message: `获取` + obj.view.title + `失败`,
    });
    return;
  }

  obj.jsonObj.leftOption.leftApiResp = data;

  putParamData(obj.jsonObj.leftOption.leftApiResp[0].id);
};

const getTableData = (params) => {
  return http.post<ResPage<{}>>('/boots/' + obj.view.hashkey + '/_griddata', params);
}

const handleItemClick = function (id) {
  proTable.value!.pageable.pageNum = 1;
  putParamData(id)
}


const putParamData = (val) => {
  eval('initParam.' + obj.jsonObj.leftOption.paramName + '=' + val);
}


const dsDlgRef = ref<InstanceType<typeof EditDs> | null>(null);

const openDsDlg = (params) => {
  dsDlgRef.value?.acceptParams({
    formData: params?params:{type:"mysql"},
    saveApi: saveDs,
    refreshApi: getDsList
  });
};


const classObject = (id) => {
  return initParam.dsid == id  ? "dsSelected" : "";
}

const del = async (id) => {
  ElMessageBox.confirm(`是否删除该` + obj.view.title + `?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const res = await delDs(id);
    ElMessage({
      type: "success",
      message: `删除` + obj.view.title + `成功!`
    });
    getLeftData()
  });
}


</script>
<style scoped lang="scss">
  .dsSelected{
    border: 1px solid var(--el-color-primary);
  }
</style>

