import { defineStore } from "pinia";
import { AuthState } from "@/stores/interface";
import {getAuthButtonListApi, getAppAuthMenuListApi, getAppInfo} from "@/api/app/loginApp";
import { getFlatMenuList, getShowMenuList, getAllBreadcrumbList } from "@/utils";
import router from "@/routers/dev";

export const useAuthAppStore = (appname) => {
  return defineStore({
    id: "zreport_auth_app_" + appname,
    state: (): AuthState => ({
      // 按钮权限列表
      authButtonList: {},
      // 菜单权限列表
      authMenuList: [],
      // 当前页面的 router name，用来做按钮权限筛选
      routeName: "",
      appinfo: {name: appname}
    }),
    getters: {
      // 按钮权限列表
      appinfoGet: state => state.appinfo,
      // 按钮权限列表
      authButtonListGet: state => state.authButtonList,
      // 菜单权限列表 ==> 这里的菜单没有经过任何处理
      authMenuListGet: state => state.authMenuList,
      // 菜单权限列表 ==> 左侧菜单栏渲染，需要剔除 isHide == true
      showMenuListGet: state => getShowMenuList(state.authMenuList),
      // 菜单权限列表 ==> 扁平化之后的一维数组菜单，主要用来添加动态路由
      flatMenuListGet: state => getFlatMenuList(state.authMenuList),
      // 递归处理后的所有面包屑导航列表
      breadcrumbListGet: state => getAllBreadcrumbList(state.authMenuList)
    },
    actions: {
      // Get AuthButtonList
      async getAppInfo() {
        const { data } = await getAppInfo(this.appinfo.name);
        this.appinfo = data;
      },
      // Get AuthButtonList
      async getAuthButtonList() {
        const { data } = await getAuthButtonListApi();
        this.authButtonList = data;
      },
      // Get AuthMenuList
      async getAuthMenuList() {
        const {data}  = await getAppAuthMenuListApi(this.appinfo.name);

        let list = [];

        data.forEach( (m) => {
          deepMenu(list,m,first);
        })

        list.push(    {
            "path": "/menuview/:key",
            "name": "menuview",
            "component": "/app/Preview",
            "meta": {
              "icon": "Menu",
              "title": "",
              "isLink": "",
              "isHide": true,
              "isFull": false,
              "isAffix": false,
              "isKeepAlive": true
            }
          }
        )
        console.log("list")
        console.log(list)
        console.log(first)
        if(first) {
          router.addRoute({
            "path" : "/index",
            "redirect": first
          })
          // list.push({
          //   "path" : "/index",
          //   "redirect": first
          // })
        }

        this.authMenuList = list;
      },
      // Set RouteName
      async setRouteName(name: string) {
        this.routeName = name;
      }
    }
  })();
};

let first = null;

const deepMenu = (list , m) => {
  console.log("deepMenu")
  console.log(m)
  let newmenu = {
    "path": m.path ,
    "name": m.name ||  m.viewkey || '',
    "redirect": m.redirect,
    "component": m.component || "/app/Preview",
    "meta": {
      "icon": m.icon,
      "title": m.title,
      "isLink": m.isLink,
      "isHide": m.isHide,
      "isFull": m.isFull,
      "isAffix": m.isAffix,
      "params" : m.viewkey? {key:m.viewkey} : null,
      "isKeepAlive": ('isKeepAlive' in m)? m.isKeepAlive:true
    },
    children: []
  }

  console.log("newmenu")
  console.log(newmenu)
  list.push(newmenu)
  if(m.children && m.children.length>0) {
    m.children.forEach((item) => {
      deepMenu(newmenu.children,item)
    })
  } else if(!first){
    first = newmenu.path;
  }
}
