{"code": 200, "data": [{"path": "/index", "name": "home", "component": "/dev/shouye", "meta": {"icon": "HomeFilled", "title": "首页", "isLink": "", "isHide": false, "isFull": false, "isAffix": true, "isKeepAlive": true}}, {"path": "/dev/ds", "name": "ds", "component": "/dev/ds/index", "meta": {"icon": "Coin", "title": "数据源", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/mis", "name": "mis", "component": "/dev/main/mis", "meta": {"icon": "Grid", "title": "MIS业务", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/bi", "name": "bi", "component": "/dev/main/bi", "meta": {"icon": "Histogram", "title": "BI 报表", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/xscreen", "name": "xscreen", "component": "/link/xscreen/index", "meta": {"icon": "Monitor", "title": "数据大屏", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/app", "name": "app", "component": "/dev/app/index", "meta": {"icon": "Apple", "title": "应用管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/tools", "name": "tools", "meta": {"icon": "MessageBox", "title": "辅助开发", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true, "expandChildren": true}, "children": [{"path": "/dev/file", "name": "file", "component": "/dev/file/index", "meta": {"icon": "<PERSON><PERSON>", "title": "资源文件", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true, "expandChildren": true}}, {"path": "/dev/enum", "name": "enum", "component": "/dev/enum/index", "meta": {"icon": "<PERSON><PERSON>", "title": "枚举字典", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/api", "name": "api", "component": "/dev/api/index", "meta": {"icon": "<PERSON><PERSON>", "title": "API 接口", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/dev/queryctl", "name": "queryctl", "component": "/dev/queryctl/index", "meta": {"icon": "<PERSON><PERSON>", "title": "自定义组件", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/menuview/gFkP00Bab7Yd4ClfmnKjebhK", "name": "menuview", "redirect": "/menuview/gFkP00Bab7Yd4ClfmnKjebhK", "meta": {"icon": "<PERSON><PERSON>", "title": "学生管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/menuview/:key", "name": "menuview", "component": "/front/Preview", "meta": {"icon": "<PERSON><PERSON>", "title": "", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isKeepAlive": true}}], "msg": "成功"}