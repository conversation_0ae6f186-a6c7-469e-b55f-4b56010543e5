<template>
  <div ref="divDom" class="main-box" :style="{'height': 'calc(100vh - 230px)','background-color': '#ffffff'}">
    <div class="card filter" :style="{'width': leftWidth + 'px'}" >
      <el-input v-model="filterText" placeholder="输入关键字进行过滤" clearable />
      <template v-if="curMain.type=='2'">
        <h4 class="title sle">数据字段
          <el-button style="float:right;margin-left:5px;margin-right:15px" circle :icon="icons.Refresh" @click="refresh" title="刷新" />
          <el-button style="float:right" circle :icon="icons.Plus"  @click="newColumn(3)" title="新增数据字段" />
        </h4>
        <el-scrollbar :style="{ height: `calc(100% - 107px)`  }">
            <template v-for="item in (list && list.length>0) ?list[0] : []">
              <div v-if="showItem(item)"
                   :class="classObject(item.id)"
                   v-on:click="editColumn(item)"
                   style="height: 40px;font-size: 14px;margin: 8px 2px">
                <span style="height:20px; display: inline-block;;margin-left: 5px">
                  <el-icon color="#6222c2" size="24"><BellFilled /></el-icon>
                  <span>{{ item.showname }}({{ item.name }})</span>
                </span>
              </div>
            </template>
        </el-scrollbar>
      </template>
      <template v-if="curMain.type=='1'" >
        <el-scrollbar :style="{ height: `calc(100% - 67px)`  }" :key="bicolumnKey">
          <div class="bicolumnContainer">
            <template v-for="(t,index) in bicolumntypes">
              <h4 class="title sle" :type="index==2?0:index+1">{{t}}
                <el-button style="float:right;margin-left:5px;margin-right:15px" circle :icon="icons.Refresh" @click="refresh" title="刷新" />
                <el-button style="float:right" circle :icon="icons.Plus"  @click="newColumn(index==2?0:index+1)" :title="'新增' + t" />
              </h4>
              <template v-for="item in  (list && list.length>index) ?list[index] : [] ">
                <div v-if="showItem(item)" class="bicolumn"
                     :id="item.id"
                     :class="classObject(item.id)"
                     v-on:click="editColumn(item)"
                     style="height: 40px;font-size: 14px;margin: 8px 2px">
                <span style="height:20px; display: inline-block;;margin-left: 5px">
                  <el-icon color="#6222c2" size="24"><BellFilled /></el-icon>
                  <span>{{ item.showname }}({{ item.name }})</span>
                </span>
                </div>
              </template>
            </template>
          </div>
        </el-scrollbar>
        <el-button type="primary" style="width:100%" @click="saveSort">保存当前位置顺序</el-button>
      </template>

    </div>
    <div :style="{'width': 'calc(100% - ' + leftWidth + 'px)' }">
      <template v-if="obj.mainid!=null">
        <el-form
          ref="formRef"
          :model="obj"
          status-icon
          :rules="columnRules"
          label-suffix=":"
          :hide-required-asterisk="false"
          style="width:90%"
          label-width="150px"
        >
          <h4 class="title sle" style="text-align: center;">{{title}}</h4>
          <template v-if="obj.type==3">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="名称" prop="name">
                  <el-input v-model="obj.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="显示名" prop="showname">
                  <el-input v-model="obj.showname" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="数据库字段" prop="dbsql">
                  <el-input type="textarea" :rows="1" v-model="obj.dbsql" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字段类型" prop="dbtype">
                  <el-select v-model="obj.dbtype" >
                    <el-option v-for="(k) in dbtypes" :key="k" :label="k" :value="k"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="解析SQL" prop="parsesql">
              <el-input type="textarea" :rows="3" v-model="obj.parsesql" />
            </el-form-item>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="枚举数据" prop="enumid">
                  <el-select v-model="obj.enumid" >
                    <el-option v-for="(k) in enumlist[0]" :key="k.value" :label="'【公有】 - ' + k.name + '/' + k.showname" :value="k.id"></el-option>
                    <el-option v-for="(k) in enumlist[1]" :key="k.value" :label="'【私有】 - ' + k.name + '/' + k.showname" :value="k.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备注" prop="memo">
                  <el-input v-model="obj.memo" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="输入校验" prop="validate">
                  <el-input v-model="obj.validate" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="默认值" prop="defaultvalue">
                  <el-input v-model="obj.defaultvalue" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="非空判断" prop="notnull">
                  <el-switch v-model="obj.notnull" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="唯一判断" prop="uniquely">
                  <el-switch v-model="obj.uniquely" active-value="1" inactive-value="0" />
                </el-form-item>
              </el-col>
            </el-row>

          </template>
          <template v-else>
            <el-form-item label="名称" prop="name">
              <el-input v-model="obj.name" />
            </el-form-item>
            <el-form-item label="显示名" prop="showname">
              <el-input v-model="obj.showname" />
            </el-form-item>
            <el-form-item label="数据库字段" prop="dbsql">
              <el-input type="textarea" :rows="1" v-model="obj.dbsql" />
            </el-form-item>
            <el-form-item label="解析SQL" prop="parsesql">
              <el-input type="textarea" :rows="3" v-model="obj.parsesql" />
            </el-form-item>
            <template v-if="obj.type==1">
              <el-form-item label="默认值" prop="defaultvalue">
                <el-input v-model="obj.defaultvalue" />
              </el-form-item>
              <el-form-item label="排序字段" prop="orderbycolumn">
                <el-input v-model="obj.orderbycolumn" />
              </el-form-item>
            </template>
            <template v-if="obj.type==2">
              <el-form-item label="汇总合计" prop="totaltype">
                <el-select v-model="obj.totaltype" >
                  <el-option v-for="(k) in totaltypelist" :key="k.value" :label="k.label" :value="k.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="数据警报" prop="alarm">
                <el-input v-model="obj.alarm" />
              </el-form-item>
              <el-form-item label="GROUP BY HAVING" prop="groupbyhaving">
                <el-input v-model="obj.groupbyhaving" />
              </el-form-item>
              <el-form-item label="备注" prop="memo">
                <el-input v-model="obj.memo" />
              </el-form-item>
            </template>
          </template>

        </el-form>
        <div style="text-align:center">
          <el-button type="danger" plain @click="delColumn">删除</el-button>
          <el-button type="primary" plain @click="cloneColumn">克隆</el-button>
          <el-button type="primary" @click="saveColumn(formRef)">保存</el-button>
        </div>
      </template>


    </div>

  </div>
</template>
<script lang="ts" setup>
import {ref, reactive, onMounted, inject} from 'vue'
import {getColumnLists,save,del,savePos} from "../../../api/dev/column";
import { getEnumList } from "../../../api/dev/enum";
import * as icons from "@element-plus/icons-vue";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";
import Sortable from "sortablejs";

// 类型 0 无用 1 维度 2 指标 3 编辑字段

onMounted(()=>{

  loadEnumList();
})

const dbtypes = [
  'string','number','date',
]

const bicolumntypes = ['数据维度','数据指标','未用']

const enumlist = ref();


const sort = () => {
  const cl = document.querySelector(".bicolumnContainer");

  //sortable对象
  new Sortable(cl, {
    animation: 150,
    draggable:  ".bicolumn",
    handle: ".bicolumn", //对grid的子对象中，带有.grid-item启动拖拽效果
  });

}

const loadEnumList = async () => {
  const { data } = await getEnumList();
  enumlist.value = data;
}

const totaltypelist = [
  {
    label:'不合计',value:'NO',
  },
  {
    label:'求和（SUM）',value:'SUM',
  },
  {
    label:'最大值（MAX）',value:'MAX',
  },
  {
    label:'最小值（MIN）',value:'MIN',
  },
  {
    label:'平均数（AVERAGE）',value:'AVERAGE',
  },
]

const leftWidth = ref(300);
const list = ref();

const title = ref();

const obj = ref({});

const formRef = ref();

const curMain = inject('curMain')


const bicolumnKey = ref(0);

const refresh = async () => {
  const { data } = await getColumnLists(curMain.id);

  list.value = data;

  if(curMain.type!='2') return ;

  bicolumnKey.value++;

  setTimeout(function(){
    const cl = document.querySelector(".bicolumnContainer");

    //sortable对象
    new Sortable(cl, {
      animation: 150,
      draggable:  ".bicolumn",
      handle: ".bicolumn", //对grid的子对象中，带有.grid-item启动拖拽效果
    });

  },200);
}

const newColumn = (type) => {
  if(type==1) {
    title.value = '新增维度';
  }else if(type == 2) {
    title.value = '新增指标';
  }else{
    title.value = '新增字段';
  }

  obj.value = {
    type: type,
    mainid: curMain.id,
  };

}
const editColumn = (item) => {
  if(item.type==1) {
    title.value = '修改维度'
  }else if(item.type==2) {
    title.value = '修改指标'
  }else{
    title.value = '修改字段'
  }
  Object.assign(obj.value,item);
}

defineExpose({
  refresh
});

const classObject = (id) => {
  return obj.value.id == id  ? "objSelected" : "";
}

const delColumn = async () => {
  if(!obj.value.id) return false;

  ElMessageBox.confirm(`是否删除该数据字段吗?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const resp = await del(obj.value.id) ;

    if(resp.code == 200) {
      ElMessage.success({ message: resp.msg });

      refresh();
    }else {
      ElMessage.error({ message: resp.msg });
    }
  }).catch(()=>{});
}



const saveColumn = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const resp = await save(obj.value) ;

      if(resp.code == 200) {
        ElMessage.success({ message: resp.msg });

        refresh();
      }else {
        ElMessage.error({ message: resp.msg });
      }

    } catch (error) {
      console.log(error);
    }
  });
}

const cloneColumn = () => {

}

const saveSort = async () => {

  const cl = document.querySelector(".bicolumnContainer");

  var l = [];
  var type;
  var orderseq = 1;
  var obj = cl.firstElementChild;
// 使用while循环遍历所有子元素
  while (obj) {
    // 处理当前子元素
    // ...
    if(obj.getAttribute('type')) {
      type = obj.getAttribute('type')
    } else if(obj.getAttribute('id')) {
      l.push({
        id: obj.getAttribute('id'),
        mainid: curMain.id,
        orderseq: orderseq++,
        type: type,
      })
    }
    obj = obj.nextElementSibling;

  }

  try {
    const resp = await savePos(l) ;

    if(resp.code == 200) {
      ElMessage.success({ message: resp.msg });

      refresh();
    }else {
      ElMessage.error({ message: resp.msg });
    }

  } catch (error) {
    console.log(error);
  }


}

const filterText = ref('');

const showItem = (item) => {
  if(filterText.value == "") return true;

  if(item.name!=null && item.name.indexOf(filterText.value)>=0) return true;
  if(item.showname!=null && item.showname.indexOf(filterText.value)>=0) return true;
  return false;
}

const columnRules = reactive({
  name: [{ required: true, message: "请输入名称" }],
});

</script>

<style scoped lang="scss">
  .objSelected{
    border: 1px solid var(--el-color-primary);
  }
</style>

