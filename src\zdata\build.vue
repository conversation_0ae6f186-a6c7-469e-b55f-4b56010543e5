<template>
  <div class="build">
    <contentmenu ref="contentmenu"></contentmenu>
    <imglist ref="imglist"
             @change="handleSetimg"></imglist>
    <!-- <headers></headers> -->
    <top ref="top"></top>
    <div class="app"
         :class="{'app--none':!menuFlag}">
      <div class="menu"
           v-show="menuFlag&&menuShow"
           @click.self="handleMouseDown">
        <p class="title">图层</p>
        <layer ref="layer"
               :nav="nav"></layer>
      </div>
      <!-- 中间区域 -->
      <div ref="section"
           class="section">
        <div class="refer-line-img"
             @click="imgClick">
          <img :src="isShowReferLine?imgOpenData:imgClose">
        </div>
        <sketch-rule :thick="thick"
                     :scale="scale"
                     :width="width"
                     :height="height"
                     :startX="startX"
                     :startY="startY"
                     :isShowReferLine="isShowReferLine"
                     :palette="palette"
                     :shadow="shadow"
                     :horLineArr="lines.h"
                     :verLineArr="lines.v" />
        <div ref='screensRef'
             id="screens"
             :class="dragSlide?'dragghanle':''"
             @mousedown.stop="dragMousedown"
             @mouseup="dragMouseup"
             @mousemove="dragMousemove"
             @wheel="handleWheel"
             @keydown.delete="handleDeleteSelect"
             @scroll="handleScroll">
          <div ref="containerRef"
               class="screen-container">
            <div class="canvas"
                 ref="canvas"
                 :style="canvasStyle">
              <container ref="container" :status="1"
                         :wscale="scale"></container>
            </div>
          </div>
        </div>
      </div>
      <div class="menu params"
           v-show="menuFlag&&paramsShow">
        <!-- <p class="title">操作</p> -->
        <br/>
        <el-tabs class="tabs"
                 stretch
                 v-model="tabsActive">
          <el-tab-pane name="0">
            <el-tooltip slot="label"
                        effect="dark"
                        content="配置"
                        disabled="true"
                        placement="top">
              <div><i class="el-icon-setting"> 配置</i></div>
            </el-tooltip>
          </el-tab-pane>
          <!-- 数据配置 -->
          <el-tab-pane name="1"
                       v-if="vaildProp('dataList')">
            <el-tooltip slot="label"
                        effect="dark"
                        content="数据"
                        disabled="true"
                        placement="top">
              <div><i class="el-icon-document-copy"> 数据</i></div>
            </el-tooltip>
          </el-tab-pane>
          <!-- 交互事件配置 -->
          <el-tab-pane name="2"
                       v-if="vaildProp('eventList')">
            <el-tooltip slot="label"
                        effect="dark"
                        content="交互"
                        disabled="true"
                        placement="top">
              <div><i class="el-icon-thumb"> 交互</i></div>
            </el-tooltip>
          </el-tab-pane>
          <!-- 其他事件配置 -->
          <el-tab-pane name="3"
                       v-if="!vaildProp('',[undefined])">
            <el-tooltip slot="label"
                        effect="dark"
                        content="事件"
                        disabled="true"
                        placement="top">
              <div><i class="iconfont icon-peizhi"> 事件</i></div>
            </el-tooltip>
          </el-tab-pane>
          <!-- 基本参数配置 -->
          <el-tab-pane name="4"
                       v-if="isActive">
            <el-tooltip slot="label"
                        effect="dark"
                        content="参数"
                        disabled="true"
                        placement="top">
              <div><i class="el-icon-folder"> 参数</i></div>
            </el-tooltip>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <codeedit @submit="codeClose"
              v-if="code.box"
              :title="code.title"
              :type="code.type"
              v-model="code.obj"
              :visible.sync="code.box"></codeedit>
  </div>

</template>
<script setup lang="ts">
import SketchRule from 'vue3-sketch-ruler'
import 'vue3-sketch-ruler/lib/style.css'
// ts需要时引入类型
import type { SketchRulerProps } from 'vue3-sketch-ruler'
import top from './group/top';

</script>
<style scoped lang="scss">

</style>
