<template>
  <el-button @click="loadComponent1">加载组件1</el-button>
  <el-button @click="loadComponent2">加载组件2</el-button>
  <KeepAlive>
    <component :is="componentName" ref="comRef" :num="num" @changeNum="changeNum"></component>
  </KeepAlive>
</template>
<script setup>
  import {ref, onMounted, reactive} from "vue";

  const componentName = ref(null);
  const num = ref(0);
  onMounted(async () => {
    // 异步加载组件
    // const moduleName = 'TestOne';
    // loadModule(moduleName);
    //
    setTimeout(() => {
      loadAllModule();
    }, 10);
  });

  const compMap = new Map();

  const loadAllModule = async () => {
    let module = await import(`@/views/dev/ds/index.vue`);
    compMap.set("a",module.default)

    module = await import(`@/views/about/index.vue`);
    compMap.set("b",module.default)

  }

  const loadComponent1 = async () => {
    componentName.value = compMap.get("a");
    if(true) return ;
    if(compMap.get("a")!=null) {
      componentName.value = compMap.get("a");
    }else {
      const module = await import(`@/views/dev/ds/index.vue`);
      componentName.value = module.default;
      compMap.set("a",module.default)
    }
  }

  const loadComponent2 = async () => {
    componentName.value = compMap.get("b");
    if(true) return ;
    if(compMap.get("b")!=null) {
      componentName.value = compMap.get("b");
    }else {
      const module = await import(`@/views/about/index.vue`);
      componentName.value = module.default;
      compMap.set("b",module.default)
    }

  }

  const changeNum = () => {
    num.value++;
  }
</script>

