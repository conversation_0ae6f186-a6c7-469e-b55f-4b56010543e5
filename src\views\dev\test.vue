<template>
  <div @click="allChecked"  class="btn">全选</div>
  <div @click="unChecked"  class="btn">反选</div>
  <div @click="getRows"  class="btn">获取选中数据</div>
  <div @click="delRows"  class="btn">删除选中数据</div>

  <div @click="getValues"  class="btn">获取选中行数据</div>
  <ag-grid-vue
    :rowData="rowData"
    :columnDefs="colDefs"
    :enableColResize="true"
    rowSelection="multiple"
    @gridReady="onGridReady"
    style="height: 500px"
    class="ag-theme-quartz"
  >
  </ag-grid-vue>

<!--  <KeepAlive>-->
<!--    <component-->
<!--      :is="prop.component.name"-->
<!--      v-bind="prop"-->
<!--    ></component>-->
<!--  </KeepAlive>-->
</template>

<script setup lang="ts" name="test">
  import {reactive, ref} from "vue";
  import "ag-grid-community/styles/ag-grid.css";
  import "ag-grid-community/styles/ag-theme-quartz.css";
  import { AgGridVue } from "ag-grid-vue3";


  // 行数据：要显示的数据
  const rowData = ref([
    { make: "Tesla", model: "Model Y", price: 64950, electric: true },
    { make: "Ford", model: "F-Series", price: 33850, electric: false },
    { make: "Toyota", model: "Corolla", price: 29600, electric: false },
  ]);

  // 列定义：定义要显示的列
  const colDefs = ref([
    { field: "make",checkboxSelection: true ,pinned: 'left'},
    { field: "model" ,pinned: 'right'},
    { field: "price" },
    { field: "electric" }
  ]);

  let params;

  const onGridReady = (p) => {
      params = p;
      params.api.sizeColumnsToFit();
  }


  const getValues = () => {
    //获取选中的数据
    var selRows=  params.api.getSelectedRows();
    if(selRows==null || selRows.length<=0)
    {
      alert("您未选中任何数据");
      return;
    }
    console.log(selRows)
    alert(selRows[0].make);
  }


  //设置全选
  const allChecked = () => {
    params.api.selectAll();
  }
  //设置反选
  const unChecked = () => {
    params.api.deselectAll();
  }
  //获取选中的数据
  const getRows = () => {
    var selRows=  params.api.getSelectedRows();
    if(selRows==null || selRows.length<=0)
    {
      alert("您未选中任何数据");
      return;
    }
    alert(JSON.stringify(selRows));
  }

  //删除选中数据
  const delRows = () => {
    //获取选中的数据
    var selRows=  params.api.getSelectedRows();
    if(selRows==null || selRows.length<=0)
    {
      alert("您未选中任何数据");
      return;
    }
    //注意调用updateRowData方法并不会更新vue的data
    // this.gridApi.updateRowData({remove: selRows});
    //正确的删除方法是这样的
    rowData.value = rowData.value.filter(item =>{
      return selRows.filter(m=>m.title===item.title).length<=0
    } );
  }



  let prop = {
    leftOption : {
      showLeft: true,            // 显示左边
      title: "标题111" ,
      showTitle: true,         // 显示搜索
      showSearch: true,         // 显示搜索
      leftWidth: 230,
      leftApi: "/dev/datasource/dsList",  // 左边API
      paramName: "dsid",           // 查询参数
    },
    tableOption : {
      tableApi: "/dev/datasource/tablelist",
      columns: [
        { type: "index", label: "#", width: 80 },
        { prop: "tablename", label: "表名",sortable:true, search: { el: "input" } },
        { prop: "tablecommon", label: "表注释",sortable:true,search: { el: "input" } },
        { prop: "operation", label: "操作", width: 300, fixed: "right" }
      ],
      pageable: {
        // 当前页数
        pageNum: 1,
        // 每页显示条数
        pageSize: 10,
        // 总条数
        total: 0,
        pageSizes: [10,20,130],
      },
      topBtns : [
        {
          text: "头部按钮1",
          type: "primary",
          plain: true,
          icon: "Grid",
          clickFunction: function(){
            alert(1)
          }
        }
      ],
      rowBtns : [
        {
          text: "行按钮",
          type: "primary",
          icon: "CirclePlus",
          clickFunction: function(row){
            console.log(row)
            alert(1)
          }
        }
      ]
    },
    component: {
      name: "ZcodeTable"
    }
  }

</script>

