<template>
  <div>
    <el-tabs v-model="tabName" class="demo-tabs" type="border-card"  @tab-change="handleClick">
      <el-tab-pane label="数据对象" name="main">
        <MainContainer ref="mainContainerRef"></MainContainer>
      </el-tab-pane>
      <el-tab-pane label="数据回调" name="callback" v-if="curMain.type==2" >
        <CallbackContainer ref="callbackContainerRef"></CallbackContainer>
      </el-tab-pane>
      <el-tab-pane label="数据字段" name="column">
        <ColumnContainer ref="columnContainerRef"></ColumnContainer>
      </el-tab-pane>
      <el-tab-pane label="关联表" name="union">
        <UnionContainer ref="unionContainerRef"></UnionContainer>
      </el-tab-pane>
      <el-tab-pane label="数据视图" name="view" style="display: flex;flex:1;flex-direction: column;height:100%;justify-content:center;align-items: stretch;">
        <ViewContainer ref="viewContainerRef"></ViewContainer>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script lang="ts" setup>
import { ref,reactive,onMounted,provide } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { useRoute } from "vue-router";

import MainSelectorDialog from './MainSelectorDialog.vue'
import ViewContainer from './ViewContainer.vue'
import MainContainer from './MainContainer.vue'
import CallbackContainer from './CallbackContainer.vue'
import ColumnContainer from './ColumnContainer.vue'
import UnionContainer from './UnionContainer.vue'
import { getMain } from '@/api/dev/main';
import { useTabsStore } from "@/stores/modules/tabs";

const viewHasRefresh = {}

const mainContainerRef = ref();
const callbackContainerRef = ref();
const columnContainerRef = ref();
const unionContainerRef = ref();
const viewContainerRef = ref();


const curMain = reactive({type:null});

const tabName = ref('view')

const route = useRoute();

const tabStore = useTabsStore();

onMounted( () => {
  load();

});

const load = async () => {
  const id = route.params.id;

  const { data } = await getMain(id);

  Object.assign(curMain, data);

  tabStore.setTabsTitle( data.showname)
  tabStore.setTabsIcon(data.type=='1'?'Histogram':'Expand')

  Object.assign(viewHasRefresh,{
    main: false,
    callback: false,
    column: false,
    union: false,
    view: false,
  })

  refreshContainer();

}

const formRef = ref();


provide('curMain',curMain);

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
  refreshContainer();
}


const refreshContainer = () => {
  if( eval ( 'viewHasRefresh.' + tabName.value ) ) return;

  eval(tabName.value + 'ContainerRef.value?.refresh()');
  eval ( 'viewHasRefresh.' + tabName.value + "=true;");
}


</script>

