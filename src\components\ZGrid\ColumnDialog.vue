<template>
  <el-dialog v-model="dialogVisible" :title="`编辑字段 - ${obj.name}`" width="80%" draggable>
    <el-form
      ref="ruleFormRef"
      label-suffix=":"
      label-position="right"
      label-width="25%"
      :model="obj"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="标题" prop="label">
            <el-input v-model="obj.label"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="宽度" prop="width">
            <el-input v-model="obj.width"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否显示" v-if="obj.prop" prop="isShow">
            <el-switch v-model="obj.isShow"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最小宽度" prop="minWidth">
            <el-input v-model="obj.minWidth"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="冻结" prop="fix">
            <el-select v-model="obj.fix">
              <el-option label="不冻结" value=""></el-option>
              <el-option label="左侧" value="left"></el-option>
              <el-option label="右侧" value="right"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortable">
            <el-switch v-model="obj.sortable"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标签展示" prop="tag">
            <el-switch v-model="obj.tag"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="拖动列宽" prop="obj.resizable">
            <el-switch v-model="obj.resizable"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="表头对齐" prop="headerAlign">
            <el-radio-group v-model="obj.headerAlign" >
              <el-radio-button value="left" label="left"></el-radio-button>
              <el-radio-button value="center" label="center"></el-radio-button>
              <el-radio-button value="right" label="right"></el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="内容对齐" prop="align">
            <el-radio-group v-model="obj.align" >
              <el-radio-button value="left" label="left"></el-radio-button>
              <el-radio-button value="center" label="center"></el-radio-button>
              <el-radio-button value="right" label="right"></el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="自定义表头" prop="headerRender">
            <el-input type="textarea" v-bind="obj.headerRender"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="内容格式化" prop="render">
            <el-input type="textarea" v-bind="obj.render"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="ColumnDialog">
import {ref, reactive, withDefaults} from "vue";
import { ElMessage, FormInstance } from "element-plus";
import _ from 'lodash-es'

const dialogVisible = ref(false);

const obj = ref({});

const searchOption = ref(false);

let impObj ;

const openDialog = (columnInfo) => {
  impObj = columnInfo;
  obj.value = {};
  obj.value = _.cloneDeep(columnInfo);

  searchOption.value = !!obj.value.search;

  dialogVisible.value = true;
};

defineExpose({ openDialog });

const handleSubmit = () => {
  // impObj = {};
  // impObj = _.cloneDeep(obj.value);
  Object.assign(impObj,obj.value);

  dialogVisible.value = false;
  console.log(impObj)
}

const searchTypes = [
  {label: '输入框',value: 'input'},
  {label: '数字输入框',value: 'input-number'},
  {label: '下拉框',value: 'select'},
  {label: '下拉框V2',value: 'select-v2'},
  {label: '树状下拉框',value: 'tree-select'},
  {label: '层叠',value: 'cascader'},
  {label: '日期选择框',value: 'date-picker'},
  {label: '时间选择框',value: 'time-picker'},
  {label: '时间下拉框',value: 'time-select'},
  {label: '开关',value: 'switch'},
  {label: '滑块',value: 'slider'},
]

const searchHandler = (item) => {
  if(item) {
    obj.value.search = obj.value._search || {};
  }else {
    obj.value._search = obj.value.search;
    obj.value.search = null;
  }

  console.log(obj.value);
}


</script>
<style >
  /*.el-form-item__label{*/
  /*  text-align: right;*/
  /*}*/
  .el-form--label-left .el-form-item__label {
    text-align: left !important;
    /* justify-content: flex-end !important; */
  }
</style>
