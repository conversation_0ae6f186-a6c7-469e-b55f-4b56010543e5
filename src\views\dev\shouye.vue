<template>
  <div>
    <div class="card mb10">
      <h4 class="title">简介</h4>
      <span class="text">
        这是一款在线的无代码编程平台，可以在线通过配置化、拖拽式的可视化开发方式，快速开发出软件系统。
        <br/>
        平台使用目前最新的技术栈开发，后端采用Java8 ，SpringBoot，Redis等技术，前端采用Vue，TypeScript等技术。
        <br/>
        平台的设计思路为数据驱动，所有的页面逻辑行为，依托于后台的数据库操作。可以轻松支持多种类型的业务开发：
        <el-link type="primary" href="">MIS 信息管理系统</el-link>&nbsp;&nbsp;&nbsp;
        <el-link type="primary" href="">BI 数据报表</el-link>&nbsp;&nbsp;&nbsp;
        <el-link type="primary" href="">数据大屏/数据看板</el-link>&nbsp;&nbsp;&nbsp;
        <el-link type="primary" href="">后台API</el-link> &nbsp;&nbsp;&nbsp; 等。
      </span>
    </div>
    <div class="card mb10">
      <h4 class="title">MIS 信息管理系统</h4>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="视图类型" label-align="left">
          <el-tag>数据列表</el-tag> <el-tag>查询面板</el-tag> <el-tag>新增/修改/查看表单</el-tag> <el-tag>关联表</el-tag> 等
        </el-descriptions-item>
        <el-descriptions-item label="按钮" label-align="left">
          <el-tag>顶部按钮</el-tag> <el-tag>行按钮</el-tag> <el-tag>底部按钮</el-tag> <el-tag>关联表按钮</el-tag> <el-tag>按钮显示控制</el-tag> <el-tag>自定义按钮</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="组件" label-align="left">
          <el-tag>输入框</el-tag> <el-tag>单选框</el-tag> <el-tag>多选框</el-tag> <el-tag>下拉框</el-tag> <el-tag>日期选择器</el-tag> 等30多种组件
          <br> <el-tag>自定义组件</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="参数传递" label-align="left">
          <el-tag>前端参数</el-tag> <el-tag>后端参数</el-tag> <el-tag>sql参数</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="数据权限" label-align="left">
          <el-tag>用户级权限</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="后端数据回调" label-align="left">
          <el-tag>数据执行</el-tag> <el-tag>数据判定</el-tag> <el-tag>删除缓存</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="card mb10">
      <h4 class="title">BI 数据报表</h4>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="视图类型" label-align="left">
          <el-tag>饼图</el-tag> <el-tag>柱状图</el-tag> <el-tag>折线图</el-tag> <el-tag>地图</el-tag> 等20多种视图
        </el-descriptions-item>
        <el-descriptions-item label="按钮" label-align="left">
          <el-tag>顶部按钮</el-tag> <el-tag>行按钮</el-tag> <el-tag>底部按钮</el-tag> <el-tag>关联表按钮</el-tag> <el-tag>按钮显示控制</el-tag> <el-tag>自定义按钮</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="组件" label-align="left">
          <el-tag>输入框</el-tag> <el-tag>单选框</el-tag> <el-tag>多选框</el-tag> <el-tag>下拉框</el-tag> <el-tag>日期选择器</el-tag> 等30多种组件
          <br> <el-tag>自定义组件</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="参数传递" label-align="left">
          <el-tag>前端参数</el-tag> <el-tag>后端参数</el-tag> <el-tag>sql参数</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="数据权限" label-align="left">
          <el-tag>用户级权限</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="后端数据回调" label-align="left">
          <el-tag>数据执行</el-tag> <el-tag>数据判定</el-tag> <el-tag>删除缓存</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="card mb10">
      <h4 class="title">数据大屏/数据看板</h4>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="视图类型" label-align="left">
          <el-tag>数据列表</el-tag> <el-tag>查询面板</el-tag> <el-tag>新增/修改/查看表单</el-tag> <el-tag>关联表</el-tag> 等
        </el-descriptions-item>
        <el-descriptions-item label="按钮" label-align="left">
          <el-tag>顶部按钮</el-tag> <el-tag>行按钮</el-tag> <el-tag>底部按钮</el-tag> <el-tag>关联表按钮</el-tag> <el-tag>按钮显示控制</el-tag> <el-tag>自定义按钮</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="组件" label-align="left">
          <el-tag>输入框</el-tag> <el-tag>单选框</el-tag> <el-tag>多选框</el-tag> <el-tag>下拉框</el-tag> <el-tag>日期选择器</el-tag> 等30多种组件
          <br> <el-tag>自定义组件</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="参数传递" label-align="left">
          <el-tag>前端参数</el-tag> <el-tag>后端参数</el-tag> <el-tag>sql参数</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="数据权限" label-align="left">
          <el-tag>用户级权限</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="后端数据回调" label-align="left">
          <el-tag>数据执行</el-tag> <el-tag>数据判定</el-tag> <el-tag>删除缓存</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

  </div>
</template>

<script setup lang="ts" name="about">
const { pkg, lastBuildTime } = __APP_INFO__;
const { dependencies, devDependencies, version } = pkg;
</script>

<style lang="scss" scoped>
.card {
  .title {
    margin: 0 0 15px;
    font-size: 17px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  .text {
    font-size: 15px;
    line-height: 25px;
    color: var(--el-text-color-regular);
    .el-link {
      font-size: 15px;
    }
  }
}
</style>
