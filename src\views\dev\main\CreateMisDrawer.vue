<template>
  <el-drawer v-model="drawerVisible" title="新增视图" size="450px">
    <el-form
      ref="formRef"
      :model="obj"
      status-icon
      :rules="columnRules"
      :hide-required-asterisk="false"
      label-width="120px"
    >
      <el-form-item label="标题 :" prop="title" >
        <el-input v-model="obj.title"></el-input>
      </el-form-item>
      <el-form-item label="视图类型 :" prop="type" >
        <el-radio-group v-model="obj.type" @change="handleChange" style="display:block">
          <el-radio  v-for="item in typeList" :value="item.value" :label="`${item.label} - ${item.value}`" size="large" style="display:block"/>
        </el-radio-group>

      </el-form-item>
      <el-form-item label=""  >
        <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts" name="CreateMisDrawer">
import {defineEmits, reactive, ref} from "vue";
import {ElMessage, FormInstance, FormRules} from "element-plus";
import { addView } from "@/api/dev/view";


const drawerVisible = ref<boolean>(false);

const obj = ref();

const typeList = [
  {
    index: 0,
    label: "表格",
    value: "ZGrid",
  },
  {
    index: 1,
    label: "新增",
    value: "add",
  },
  {
    index: 2,
    label: "修改",
    value: "edit",
  },
  {
    index: 3,
    label: "详情",
    value: "detail",
  },
  {
    index: 4,
    label: "查询",
    value: "query",
  },
]


const openDrawer = (param) => {
  obj.value = param;
  drawerVisible.value = true;
};

const emit = defineEmits(['save'])

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {

      const resp = await addView(obj.value) ;

      if(resp.code===200){
        ElMessage.success({ message: resp.msg });
      }else {
        ElMessage.error({ message: resp.msg });
      }


      drawerVisible.value = false;
      emit('save');
    } catch (error) {
      console.log(error);
    }
  });
}

const formRef = ref();

const handleChange = (k) => {
  console.log(k);

  switch(k) {
    case 'ZGrid':
      obj.value.title = obj.value.mainname + '管理'
      break;
    case 'add':
      obj.value.title = '新增' + obj.value.mainname
      break;
    case 'edit':
      obj.value.title = '修改' + obj.value.mainname
      break;
    case 'detail':
      obj.value.title = obj.value.mainname + '详情'
      break;
    case 'query':
      obj.value.title = '查询' + obj.value.mainname
      break;
  }
}

const columnRules = reactive({
  title: [{ required: true, message: "请输入标题" }],
});

defineExpose({
  openDrawer
});
</script>

