<!-- 柱状图配置 -->
<template>
  <div>
    <el-form-item label="显示左侧">
      <el-switch v-model="obj.jsonObj.leftOption.showLeft"></el-switch>
    </el-form-item>
    <template v-if="obj.jsonObj.leftOption.showLeft">
      <el-form-item label="标题" >
        <el-input v-model="obj.view.title"></el-input>
      </el-form-item>
      <el-form-item label="显示标题">
        <el-switch v-model="obj.jsonObj.leftOption.showTitle"></el-switch>
      </el-form-item>
      <el-form-item label="显示搜索">
        <el-switch v-model="obj.jsonObj.leftOption.showSearch"></el-switch>
      </el-form-item>
      <el-form-item label="左侧宽度" >
        <el-input v-model="obj.jsonObj.leftOption.leftWidth"  placeholder="请输入左侧宽度" >
          <template #append>px</template>
        </el-input>
      </el-form-item>
      <el-form-item label="左侧API" >
        <el-input type="textarea" v-model="obj.jsonObj.leftOption.leftApi"  placeholder="请输入左侧API" ></el-input>
      </el-form-item>
      <el-form-item label="左侧传参" >
        <el-input v-model="obj.jsonObj.leftOption.paramName"  placeholder="请输入左侧传参" ></el-input>
      </el-form-item>
    </template>

    <el-form-item label="是否分页">
      <el-switch v-model="obj.jsonObj.tableOption.pagination"></el-switch>
    </el-form-item>
    <template v-if="obj.jsonObj.tableOption.pagination">
      <el-form-item label="分页" >
        <draggable v-model="obj.jsonObj.tableOption.pageable.pageSizes" itemKey="" >
          <template #item="{element,index}">
            <div>
              <el-button type="success" link :icon="icons.Pointer"></el-button>
              <el-input-number v-model="obj.jsonObj.tableOption.pageable.pageSizes[index]" ></el-input-number>
              <el-button type="primary" link :icon="icons.Delete" style="font-size: 12px" @click="obj.jsonObj.tableOption.pageable.pageSizes.splice(index,1)"> 删除</el-button>
            </div>
          </template>
        </draggable>
        <el-button type="primary" :icon="icons.CirclePlus" style="font-size: 12px" @click="obj.jsonObj.tableOption.pageable.pageSizes.push(obj.jsonObj.tableOption.pageable.pageSizes.slice(-1)[0]+10)"> 增加</el-button>
      </el-form-item>
      <el-form-item label="每页条数" >
        <el-select v-model="obj.jsonObj.tableOption.pageable.pageSize" placeholder="请选择">
          <el-option
            v-for="item in obj.jsonObj.tableOption.pageable.pageSizes"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
    </template>

    <el-form-item label="横向斑马纹">
      <el-switch v-model="obj.jsonObj.tableOption.stripe"></el-switch>
    </el-form-item>
    <el-form-item label="纵向边框">
      <el-switch v-model="obj.jsonObj.tableOption.border"></el-switch>
    </el-form-item>
    <el-form-item label="显示工具栏">
      <el-switch v-model="obj.jsonObj.tableOption.toolButton" v-if="!btnToggle"></el-switch>
      <el-button v-if="obj.jsonObj.tableOption.toolButton" link style="width:80%" @click="btnToggle=!btnToggle">展开<el-icon class="el-icon--right"><ArrowUp v-if="btnToggle"/><ArrowDown v-else/></el-icon></el-button>
        <template v-if="btnToggle " v-for="(item,index) in btnList" >
          <div style="width:100%">
            <el-checkbox v-model="topBtns" :label="item"></el-checkbox>
          </div>
        </template>
    </el-form-item>
<!--    <el-form-item label="表格API" >-->
<!--      <el-input type="textarea" v-model="obj.jsonObj.tableOption.tableApi"  placeholder="请输入表格API" ></el-input>-->
<!--    </el-form-item>-->
<!--    <el-form-item label-width="0" >-->
<!--      <el-button type="primary" class="block" @click="showTableApiResp">查看返回结果</el-button>-->
<!--    </el-form-item>-->

    <el-form-item label="索引列">
      <el-radio-group v-model="obj.jsonObj.tableOption.pkType">
        <el-radio-button label="单选" value="radio" />
        <el-radio-button label="多选" value="selection" />
        <el-radio-button label="序号" value="index" />
      </el-radio-group>
    </el-form-item>

    <p>字段配置<el-checkbox style="float:right" @change="selectAllColumn" label="全选"></el-checkbox></p>
    <el-form-item label-width="0" >
      <draggable v-model="obj.jsonObj.tableOption.columns" itemKey="name"  style="width: 100%">
        <template #item="{element,index}" style="width: 100%">
          <el-row :key="index" :class="getRowClass(index)">
            <el-col :span="12">
              <label class="el-col-24" >
                <el-checkbox v-model="element.selected" ></el-checkbox>
                {{element.label}}
              </label>
            </el-col>
            <el-col :span="10" style="display: inline-block">{{element.prop}}</el-col>
            <el-col :span="2" style="font-size: 44px">
              <el-link type="primary" link :icon="icons.Edit" @click="editColumn(element)" title="编辑"></el-link>
            </el-col>
          </el-row>
        </template>
      </draggable>
    </el-form-item>


    <p>头部按钮</p>
    <el-form-item label-width="0" >
      <draggable v-model="obj.jsonObj.tableOption.topBtns" itemKey="text"  style="width: 100%">
        <template #item="{element,index}" style="width: 100%">
          <div style="width: 100%;border-bottom: 1px solid #cdcdcd">
            <span style="width: 40%;display: inline-block">{{element.label}}</span>
            <span style="width: 40%;display: inline-block">{{element.type}}</span>
            <span style="width: 20%;font-size: 12px">
              <el-button type="primary" link :icon="icons.Edit" @click="element.isTop=true;editBtn(element)" title="编辑"></el-button>
              <el-button type="danger" link :icon="icons.Delete" title="删除" @click="obj.jsonObj.tableOption.topBtns.splice(index,1)"></el-button>
            </span>
          </div>
        </template>
      </draggable>
      <el-button type="primary" :icon="icons.CirclePlus" style="font-size: 12px" @click="obj.jsonObj.tableOption.topBtns.push({label:'头部按钮',type:'primary'})"> 增加头部按钮</el-button>
    </el-form-item>

    <p>行按钮</p>
    <el-form-item label-width="0" >
      <draggable v-model="obj.jsonObj.tableOption.rowBtns" itemKey="text"  style="width: 100%">
        <template #item="{element,index}" style="width: 100%">
          <div style="width: 100%;border-bottom: 1px solid #cdcdcd">
            <span style="width: 40%;display: inline-block">{{element.label}}</span>
            <span style="width: 40%;display: inline-block">{{element.type}}</span>
            <span style="width: 20%;font-size: 12px">
              <el-button type="primary" link :icon="icons.Edit" @click="editBtn(element)" title="编辑"></el-button>
              <el-button type="danger" link :icon="icons.Delete" title="删除" @click="obj.jsonObj.tableOption.rowBtns.splice(index,1)"></el-button>
            </span>
          </div>
        </template>
      </draggable>
      <el-button type="primary" :icon="icons.CirclePlus" style="font-size: 12px" @click="obj.jsonObj.tableOption.rowBtns.push({label:'行按钮'})"> 增加行按钮</el-button>
    </el-form-item>

    <el-dialog
      v-model="codeRef.show"
      :title="codeRef.title"
      :destroy-on-close="true"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      draggable
    >
      <MonacoEditor v-model="codeRef.code" language="json" style="height:400px"/>

<!--      <Coder style="width:100%;height:400px" v-bind="codeProp" :modelValue="codeRef.code" ></Coder>-->

<!--        <Coder :value="codeRef.code" :language="codeRef.language"></Coder>-->
<!--      <ViteMonacoPlugin></ViteMonacoPlugin>-->
<!--      <el-form size="small"-->
<!--               v-if="codeRef.show"-->
<!--               label-width="130px">-->
<!--        <el-form-item label-width="0" >-->
<!--          <el-input type="textarea"  v-model="codeRef.code"-->
<!--                        language="json"-->
<!--                         disabled-->
<!--                         height="440"></el-input>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
    </el-dialog>
    <ZDialog ref="dlgRef"></ZDialog>
    <ColumnDialog ref="columnDialogRef" ></ColumnDialog>
    <BtnDialog ref="btnDialogRef" ></BtnDialog>
  </div>
</template>

<script setup lang="tsx" name="ZcodeTableOption">
import {inject, onMounted, reactive, ref, watch} from "vue";
import _ from 'lodash-es'
import { Props } from "./interface"
import * as icons from "@element-plus/icons-vue";
import draggable from 'vuedraggable';
import http from "@/api";
import ZDialog from '@/components/ZDialog/index.vue'
// import ViteMonacoPlugin from 'vite-plugin-monaco-editor'
// import Coder from '@/components/Coder/index.vue';
import MonacoEditor from '@/components/MonacoEditor/index.vue'
import ColumnDialog from '@/packages/ZcodeTable/ColumnDialog.vue'
import BtnDialog from '@/packages/ZcodeTable/BtnDialog.vue'
import { getColumnListNoPk } from '@/api/dev/column.ts'

// import { registerProvider } from '@/components/Coder/registerCompletion'
// const registerPro = registerProvider('sql', obj.jsonObj.constModelData)


let dlgRef = ref({});



const openDlg = (param) => {
  dlgRef.value?.openDlg(param);
}



// const MonacoEditRef = ref<InstanceType<typeof MonacoEditor>>()


let drag = ref(false);

let codeRef = ref({
  title: "",
  show: false,
  language: "json",
  code: null
})

let btnToggle = ref(false);


const btnList = ["refresh" , "setting" , "search"]

let topBtns = ref([]);

watch(topBtns,(o,n) => {
  obj.jsonObj.tableOption.toolButton = topBtns;
})

const showTableApiResp =  () => {

  codeRef.value.code = JSON.stringify(obj.jsonObj.tableOption.tableApiResp);
  codeRef.value.show = true;
}

const columnDialogRef = ref(null);

const editColumn = (column) => {
  columnDialogRef.value?.openDialog(column);
}

const btnDialogRef = ref(null);


const editBtn = (btn) => {
  btnDialogRef.value?.openDialog(btn);
}


const obj = inject('obj',reactive({
  view: {},
  jsonObj: {},
}));

const defaultObj ={
  leftOption : {
    showLeft: false,
      showTitle: true,
      showSearch: true,
      leftWidth: 230,
      paramName: "id",
  },
  tableOption: {
    tableApi: '/boot/' + obj.view.hashkey + '/_griddata',
    pagination: true,
    border: true,
    pkType: 'radio',
    columns: [],
    toolButton: [],
    topBtns: [],
    rowBtns: [],
    pageable: {
      // 当前页数
      pageNum: 1,
      // 每页显示条数
      pageSize: 10,
      // 总条数
      total: 0,
      pageSizes: [10,20,30],
    },
    rowKey: 'id',
  }
}

obj.jsonObj = _.merge({},defaultObj,obj.jsonObj);

const columnList = ref([]);

onMounted(async () => {
  const { data } = await getColumnListNoPk(obj.view.mainid);
  columnList.value = data;
  columnList.value.push({
    name: 'operation',
    showname: '操作',
  })

  columnList.value.forEach(item => {
    console.log(item);
    if(containColumn(obj.jsonObj.tableOption.columns,item)) return;
    obj.jsonObj.tableOption.columns.push({
      prop: item.name,
      label: item.showname,
    })
  })

  var j = obj.jsonObj.tableOption.columns.length-1;

  for(;j>=0;j--) {
    var c = obj.jsonObj.tableOption.columns[j];
    if(containC(columnList.value,c))continue;
    obj.jsonObj.tableOption.columns.splice(j,1);
  }

})

const containColumn = (list,c) => {
  for(var i =0;i<list.length;i++) {
    if(list[i].prop === c.name) return true;
  }
  return false;
}
const containC = (list,c) => {
  for(var i =0;i<list.length;i++) {
    if(list[i].name === c.prop) return true;
  }
  return false;
}


const selectAllColumn = (val) => {
  obj.jsonObj.tableOption.columns.forEach((item) => {
    item.selected = !!val;
  })
}

const getRowClass = (index) => {
  return index % 2 === 0 ? 'even-row' : 'odd-row';
}


</script>

<style>
  .block{
    margin: 0 auto;
    width: 92%;
    display: block;
  }

  .even-row {
    background-color: #f2f2f2; /* 偶数行背景色 */
  }
  .odd-row {
    background-color: #ffffff; /* 奇数行背景色 */
  }

</style>
