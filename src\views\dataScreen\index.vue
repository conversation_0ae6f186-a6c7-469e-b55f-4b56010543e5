<template>
  <div class="dataScreen-container">
    <!-- 顶部工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button @click="router.push(HOME_URL)" type="primary" size="small">
          <el-icon><ArrowLeft /></el-icon>
          返回首页
        </el-button>
        <el-button @click="previewMode = !previewMode" :type="previewMode ? 'success' : 'info'" size="small">
          <el-icon><View /></el-icon>
          {{ previewMode ? "退出预览" : "预览" }}
        </el-button>
      </div>
      <div class="toolbar-center">
        <h2>数据大屏设计器</h2>
      </div>
      <div class="toolbar-right">
        <el-button @click="saveDesign" type="success" size="small">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
        <el-button @click="exportDesign" type="warning" size="small">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <span class="header-time">{{ time }}</span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="designer-main" v-if="!previewMode">
      <!-- 左侧组件库 -->
      <div class="designer-sidebar-left">
        <div class="sidebar-title">组件库</div>
        <el-collapse v-model="activeCollapse" accordion>
          <el-collapse-item title="图表组件" name="charts">
            <div class="component-list">
              <div
                v-for="chart in chartComponents"
                :key="chart.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart($event, chart)"
              >
                <div class="component-icon">
                  <el-icon><component :is="chart.icon" /></el-icon>
                </div>
                <span class="component-name">{{ chart.name }}</span>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item title="数据组件" name="data">
            <div class="component-list">
              <div
                v-for="data in dataComponents"
                :key="data.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart($event, data)"
              >
                <div class="component-icon">
                  <el-icon><component :is="data.icon" /></el-icon>
                </div>
                <span class="component-name">{{ data.name }}</span>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item title="装饰组件" name="decoration">
            <div class="component-list">
              <div
                v-for="decoration in decorationComponents"
                :key="decoration.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart($event, decoration)"
              >
                <div class="component-icon">
                  <el-icon><component :is="decoration.icon" /></el-icon>
                </div>
                <span class="component-name">{{ decoration.name }}</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 中间画布区域 -->
      <div class="designer-canvas-container">
        <div class="canvas-toolbar">
          <el-button-group size="small">
            <el-button @click="zoomOut">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button>{{ Math.round(canvasScale * 100) }}%</el-button>
            <el-button @click="zoomIn">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>
          <el-button @click="resetCanvas" size="small">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        <div
          class="designer-canvas"
          ref="canvasRef"
          :style="{ transform: `scale(${canvasScale})` }"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @click="selectComponent(null)"
        >
          <grid-layout
            v-model:layout="layout"
            :col-num="24"
            :row-height="30"
            :is-draggable="true"
            :is-resizable="true"
            :vertical-compact="false"
            :margin="[10, 10]"
            :use-css-transforms="true"
          >
            <grid-item
              v-for="item in layout"
              :key="item.i"
              :x="item.x"
              :y="item.y"
              :w="item.w"
              :h="item.h"
              :i="item.i"
              :class="{ selected: selectedComponent?.i === item.i }"
              @click.stop="selectComponent(item)"
            >
              <div class="component-wrapper">
                <div class="component-header" v-if="selectedComponent?.i === item.i">
                  <span class="component-title">{{ getComponentName(item.componentType) }}</span>
                  <el-button @click.stop="removeComponent(item.i)" type="danger" size="small" :icon="Delete" circle />
                </div>
                <component :is="getComponentByType(item.componentType)" :config="item.config" :style="item.style" />
              </div>
            </grid-item>
          </grid-layout>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="designer-sidebar-right">
        <div class="sidebar-title">属性配置</div>
        <div v-if="selectedComponent" class="property-panel">
          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="基础属性" name="basic">
              <div class="property-section">
                <el-form label-width="80px" size="small">
                  <el-form-item label="组件名称">
                    <el-input v-model="selectedComponent.name" />
                  </el-form-item>
                  <el-form-item label="宽度">
                    <el-input-number v-model="selectedComponent.w" :min="1" :max="24" />
                  </el-form-item>
                  <el-form-item label="高度">
                    <el-input-number v-model="selectedComponent.h" :min="1" />
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
            <el-tab-pane label="样式配置" name="style">
              <div class="property-section">
                <el-form label-width="80px" size="small">
                  <el-form-item label="背景色">
                    <el-color-picker v-model="selectedComponent.style.backgroundColor" />
                  </el-form-item>
                  <el-form-item label="边框">
                    <el-input v-model="selectedComponent.style.border" />
                  </el-form-item>
                  <el-form-item label="圆角">
                    <el-input v-model="selectedComponent.style.borderRadius" />
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
            <el-tab-pane label="数据配置" name="data">
              <div class="property-section">
                <el-form label-width="80px" size="small">
                  <el-form-item label="数据源">
                    <el-select v-model="selectedComponent.config.dataSource" placeholder="选择数据源">
                      <el-option label="静态数据" value="static" />
                      <el-option label="API接口" value="api" />
                      <el-option label="实时数据" value="realtime" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="数据配置" v-if="selectedComponent.config.dataSource === 'static'">
                    <el-input
                      v-model="selectedComponent.config.staticData"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入JSON格式数据"
                    />
                  </el-form-item>
                  <el-form-item label="API地址" v-if="selectedComponent.config.dataSource === 'api'">
                    <el-input v-model="selectedComponent.config.apiUrl" placeholder="请输入API地址" />
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div v-else class="no-selection">
          <el-empty description="请选择一个组件进行配置" />
        </div>
      </div>
    </div>

    <!-- 预览模式 -->
    <div class="preview-container" v-else>
      <div class="dataScreen-content" ref="dataScreenRef">
        <div class="dataScreen-header">
          <div class="header-lf">
            <span class="header-screening">数据大屏</span>
          </div>
          <div class="header-ct">
            <div class="header-ct-title">
              <span>智慧数据可视化展示平台</span>
            </div>
          </div>
          <div class="header-ri">
            <span class="header-time">当前时间：{{ time }}</span>
          </div>
        </div>
        <div class="dataScreen-main">
          <grid-layout
            v-model:layout="layout"
            :col-num="24"
            :row-height="30"
            :is-draggable="false"
            :is-resizable="false"
            :vertical-compact="false"
            :margin="[10, 10]"
            :use-css-transforms="true"
          >
            <grid-item v-for="item in layout" :key="item.i" :x="item.x" :y="item.y" :w="item.w" :h="item.h" :i="item.i">
              <component :is="getComponentByType(item.componentType)" :config="item.config" :style="item.style" />
            </grid-item>
          </grid-layout>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="dataScreen">
import { ref, onMounted, onBeforeUnmount, reactive, markRaw } from "vue";
import { HOME_URL } from "@/config";
import { useRouter } from "vue-router";
import { GridLayout, GridItem } from "vue-grid-layout";
import { ElMessage } from "element-plus";
import {
  ArrowLeft,
  View,
  Document,
  Download,
  ZoomOut,
  ZoomIn,
  Refresh,
  Delete,
  TrendCharts,
  PieChart,
  DataLine,
  Grid,
  Picture,
  DocumentCopy,
  Notification,
  Histogram
} from "@element-plus/icons-vue";
import dayjs from "dayjs";

// 导入现有图表组件
import AgeRatioChart from "./components/AgeRatioChart.vue";
import AnnualUseChart from "./components/AnnualUseChart.vue";
import ChinaMapChart from "./components/ChinaMapChart.vue";
import HotPlateChart from "./components/HotPlateChart.vue";
import MaleFemaleRatioChart from "./components/MaleFemaleRatioChart.vue";
import OverNext30Chart from "./components/OverNext30Chart.vue";
import PlatformSourceChart from "./components/PlatformSourceChart.vue";
import RealTimeAccessChart from "./components/RealTimeAccessChart.vue";

// 导入新的数据组件
import DataCard from "./components/DataCard.vue";
import DataTable from "./components/DataTable.vue";
import TextComponent from "./components/TextComponent.vue";

const router = useRouter();
const dataScreenRef = ref<HTMLElement | null>(null);
const canvasRef = ref<HTMLElement | null>(null);

// 设计器状态
const previewMode = ref(false);
const canvasScale = ref(1);
const activeCollapse = ref("charts");
const activeTab = ref("basic");
const selectedComponent = ref<any>(null);

// 布局数据
const layout = ref<any[]>([]);

// 组件库定义
const chartComponents = reactive([
  { type: "realTimeChart", name: "实时访问图", icon: markRaw(TrendCharts), component: markRaw(RealTimeAccessChart) },
  { type: "pieChart", name: "饼图", icon: markRaw(PieChart), component: markRaw(MaleFemaleRatioChart) },
  { type: "barChart", name: "柱状图", icon: markRaw(Histogram), component: markRaw(AgeRatioChart) },
  { type: "lineChart", name: "折线图", icon: markRaw(DataLine), component: markRaw(OverNext30Chart) },
  { type: "mapChart", name: "地图", icon: markRaw(Grid), component: markRaw(ChinaMapChart) },
  { type: "annualChart", name: "年度对比", icon: markRaw(TrendCharts), component: markRaw(AnnualUseChart) },
  { type: "platformChart", name: "平台统计", icon: markRaw(PieChart), component: markRaw(PlatformSourceChart) },
  { type: "hotChart", name: "热门排行", icon: markRaw(Histogram), component: markRaw(HotPlateChart) }
]);

const dataComponents = reactive([
  { type: "dataCard", name: "数据卡片", icon: markRaw(DocumentCopy), component: markRaw(DataCard) },
  { type: "dataTable", name: "数据表格", icon: markRaw(Grid), component: markRaw(DataTable) },
  { type: "dataList", name: "数据列表", icon: markRaw(Notification), component: markRaw(DataTable) }
]);

const decorationComponents = reactive([
  { type: "border", name: "边框", icon: markRaw(Picture), component: markRaw(TextComponent) },
  { type: "background", name: "背景", icon: markRaw(Picture), component: markRaw(TextComponent) },
  { type: "text", name: "文本", icon: markRaw(DocumentCopy), component: markRaw(TextComponent) }
]);

// 拖拽相关
let draggedComponent: any = null;

const handleDragStart = (event: DragEvent, component: any) => {
  draggedComponent = component;
  event.dataTransfer!.effectAllowed = "copy";
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  event.dataTransfer!.dropEffect = "copy";
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  if (!draggedComponent) return;

  const rect = canvasRef.value!.getBoundingClientRect();
  const x = Math.floor((event.clientX - rect.left) / (rect.width / 24));
  const y = Math.floor((event.clientY - rect.top) / 30);

  const newComponent = {
    i: `component_${Date.now()}`,
    x: Math.max(0, Math.min(x, 20)),
    y: Math.max(0, y),
    w: 6,
    h: 8,
    componentType: draggedComponent.type,
    name: draggedComponent.name,
    config: {
      dataSource: "static",
      staticData: "{}",
      apiUrl: "",
      title: draggedComponent.name
    },
    style: {
      backgroundColor: "rgba(255, 255, 255, 0.1)",
      border: "1px solid #409eff",
      borderRadius: "4px",
      padding: "10px"
    }
  };

  layout.value.push(newComponent);
  draggedComponent = null;

  ElMessage.success(`添加${draggedComponent?.name || "组件"}成功`);
};

// 组件操作
const selectComponent = (component: any) => {
  selectedComponent.value = component;
};

const removeComponent = (componentId: string) => {
  const index = layout.value.findIndex(item => item.i === componentId);
  if (index > -1) {
    layout.value.splice(index, 1);
    selectedComponent.value = null;
    ElMessage.success("删除组件成功");
  }
};

const getComponentByType = (type: string) => {
  const allComponents = [...chartComponents, ...dataComponents, ...decorationComponents];
  const component = allComponents.find(c => c.type === type);
  return component?.component || "div";
};

const getComponentName = (type: string) => {
  const allComponents = [...chartComponents, ...dataComponents, ...decorationComponents];
  const component = allComponents.find(c => c.type === type);
  return component?.name || "未知组件";
};

// 画布操作
const zoomIn = () => {
  canvasScale.value = Math.min(canvasScale.value + 0.1, 2);
};

const zoomOut = () => {
  canvasScale.value = Math.max(canvasScale.value - 0.1, 0.5);
};

const resetCanvas = () => {
  canvasScale.value = 1;
  layout.value = [];
  selectedComponent.value = null;
  ElMessage.success("画布已重置");
};

// 设计器操作
const saveDesign = () => {
  const designData = {
    layout: layout.value,
    timestamp: Date.now()
  };
  localStorage.setItem("dataScreenDesign", JSON.stringify(designData));
  ElMessage.success("保存成功");
};

const exportDesign = () => {
  const designData = {
    layout: layout.value,
    timestamp: Date.now()
  };
  const blob = new Blob([JSON.stringify(designData, null, 2)], { type: "application/json" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `datascreen_design_${dayjs().format("YYYY-MM-DD_HH-mm-ss")}.json`;
  a.click();
  URL.revokeObjectURL(url);
  ElMessage.success("导出成功");
};

// 初始化和生命周期
onMounted(() => {
  // 加载保存的设计
  const savedDesign = localStorage.getItem("dataScreenDesign");
  if (savedDesign) {
    try {
      const designData = JSON.parse(savedDesign);
      layout.value = designData.layout || [];
    } catch (error) {
      console.error("加载设计失败:", error);
    }
  }

  // 预览模式的响应式设置
  if (dataScreenRef.value && previewMode.value) {
    dataScreenRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
    dataScreenRef.value.style.width = `1920px`;
    dataScreenRef.value.style.height = `1080px`;
  }
  window.addEventListener("resize", resize);
});

// 设置响应式
const resize = () => {
  if (dataScreenRef.value && previewMode.value) {
    dataScreenRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
  }
};

// 根据浏览器大小推断缩放比例
const getScale = (width = 1920, height = 1080) => {
  let ww = window.innerWidth / width;
  let wh = window.innerHeight / height;
  return ww < wh ? ww : wh;
};

// 获取当前时间
let timer: NodeJS.Timer | null = null;
let time = ref<string>(dayjs().format("YYYY年MM月DD HH:mm:ss"));
timer = setInterval(() => {
  time.value = dayjs().format("YYYY年MM月DD HH:mm:ss");
}, 1000);

onBeforeUnmount(() => {
  window.removeEventListener("resize", resize);
  clearInterval(timer as unknown as number);
});
</script>
<style lang="scss" scoped>
@import "./index";
@import "./designer";
</style>
