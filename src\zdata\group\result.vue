<template>
  <el-dialog append-to-body
             @open="open"
             title="导入导出"
             :close-on-click-modal="false"
             :visible.sync="show"
             width="60%">
    <monaco-editor v-model="json"></monaco-editor>
    <span slot="footer"
          class="dialog-footer">
      <el-button @click="importData"
                 size="small"
                 type="primary">导入数据</el-button>
      <el-button @click="exportData"
                 size="small"
                 type="danger">导出数据</el-button>
    </span>

  </el-dialog>

</template>
<script setup lang="ts">
import MonacoEditor from '@/page/components/editor'

</script>

<style scoped lang="scss">

</style>
