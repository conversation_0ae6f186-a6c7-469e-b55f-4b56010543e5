import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 获取字段列表
export const getColumnLists = (mid) => {
  return http.get(PORT1 + `/../dev/column/getColumnLists?mid=` + mid );
};

// 获取字段列表
export const getColumnList = (mid) => {
  return http.get(PORT1 + `/../dev/column/getColumnList?mid=` + mid );
};

// 获取字段列表（不包含主键）
export const getColumnListNoPk = (mid) => {
  return http.get(PORT1 + `/../dev/column/getColumnListNoPk?mid=` + mid );
};


// 保存字段
export const save = (params) => {
  return http.post(PORT1 + `/../dev/column/save` ,params);
};

// 保存字段顺序
export const savePos = (params) => {
  return http.post(PORT1 + `/../dev/column/savePos` ,params);
};

// 删除字段
export const del = (id) => {
  return http.get(PORT1 + `/../dev/column/del?id=` + id );
};





