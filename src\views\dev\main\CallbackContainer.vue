<template>
  <div ref="divDom" class="main-box" :style="{'height': 'calc(100vh - 260px)','background-color': '#ffffff'}">
<!--  <div ref="divDom" class="main-box" style="height: 100vh;background-color: #ffffff">-->
    <div class="card filter" :style="{'width': leftWidth + 'px'}" >
      <h4 class="title sle">数据回调
        <el-button style="float:right;margin-left:5px" circle :icon="icons.Refresh" @click="refresh" title="刷新" />
        <el-button style="float:right" circle :icon="icons.Plus"  @click="newCallback" title="新增数据回调" />
      </h4>
      <el-input v-if="list && list.length>0" v-model="filterText" placeholder="输入关键字进行过滤" clearable />
      <el-scrollbar :style="{ height: `calc(100% - 107px)`  }">
        <template v-for="item in list">
          <div v-if="showItem(item)"
               :id="item.id"
               :class="classObject(item.id)"
               v-on:click="editCallback(item)"
               style="height: 80px;font-size: 14px;margin: 8px 2px">
            <div style="width: 100%;height:60px; display: inline-block;;margin-left: 5px">
              <span>{{ item.remark }}</span>
              <span style="float:right;margin:5px 8px 0 0 "><el-tag :type="btnTypes[Number(item.category)]">{{callbackTypeList[Number(item.category)].label}}</el-tag> </span>
              <div >{{item.type}}</div>
<!--              <div style="display: flex;flex-direction: row;flex-wrap:nowrap">-->
<!--                <el-tag v-for="(o,index) in item.types"  :type="btnTypes[index % btnTypes.length]">{{o}}</el-tag>-->
<!--              </div>-->
            </div>
          </div>
        </template>
      </el-scrollbar>
    </div>
    <div :style="{'width': 'calc(100% - ' + leftWidth + 'px)' }">
      <el-form
        ref="formRef"
        :model="obj"
        status-icon
        :rules="columnRules"
        label-suffix=":"
        :hide-required-asterisk="false"
        style="width:80%"
        label-width="20%"
      >
        <h4 class="title sle" style="text-align: center;">{{op}}数据回调</h4>
        <el-form-item label="名称" prop="remark" >
          <el-input v-model="obj.remark"></el-input>
        </el-form-item>
        <el-form-item label="操作" prop="category" >
          <el-radio-group v-model="obj.category" @change="changeCategory">
            <el-radio-button v-for="(t) in callbackTypeList" :label="t.label" :value="t.value" :disabled="obj.id && obj.category != t.value" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="类型" prop="types" >
          <el-checkbox-group v-model="obj.types" >
            <el-checkbox-button v-for="(t) in subType" :label="t.label" :value="t.value"  />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="提示语" prop="tips" v-if="obj.category==1">
          <el-input v-model="obj.tips"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort" >
          <el-input-number v-model="obj.sort" :min="1" :max="10"></el-input-number>
        </el-form-item>
        <el-form-item label="缓存key值" prop="thesql" v-if="obj.category==2" >
          <el-input type="textarea" v-model="obj.thesql" :rows="10"></el-input>
        </el-form-item>
        <el-form-item label="SQL" prop="thesql" v-else>
          <el-input type="textarea" v-model="obj.thesql" :rows="10"></el-input>
        </el-form-item>

        <el-form-item label=""  >
          <el-button type="primary" plain @click="delCallback">删除</el-button>
          <el-button type="primary" @click="saveCallback(formRef)">保存</el-button>
        </el-form-item>
      </el-form>

    </div>

  </div>
</template>
<script lang="ts" setup>
import {ref, reactive, onMounted, inject} from 'vue'
import {getCallbackList,save,del} from "../../../api/dev/callback";
import * as icons from "@element-plus/icons-vue";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";
import { btnTypes } from "@/config/dicConfig.js";

onMounted(()=>{
  newCallback();
})

const columnRules = reactive({
  remark: [{ required: true, message: "请输入名称" }],
  types: [{ required: true, message: "请选择类型" }],
  thesql: [{ required: true, message: "请输入内容" }],
});

const callbackTypeList = [
  {
    label: "数据执行", value: 0,
    type: [
      {
        label: "新增前执行", value: "insertBefore",
      },
      {
        label: "新增后执行", value: "insertAfter",
      },
      {
        label: "修改前执行", value: "updateBefore",
      },
      {
        label: "修改后执行", value: "updateAfter",
      },
      {
        label: "删除前执行", value: "deleteBefore",
      },
      {
        label: "删除后执行", value: "deleteAfter",
      },
    ]
  },
  {
    label: "数据判定", value: 1,
    type: [
      {
        label: "新增前判断", value: "insertDecide",
      },
      {
        label: "修改前执行", value: "updateDecide",
      },
      {
        label: "删除前判断", value: "deleteDecide",
      },
    ]
  },
  {
    label: "删除缓存", value: 2,
    type: [
      {
        label: "新增后删除缓存", value: "insertDelcache",
      },
      {
        label: "修改后删除缓存", value: "updateDelcache",
      },
      {
        label: "删除后删除缓存", value: "deleteDelcache",
      },
    ]
  },
]

const subType = ref(callbackTypeList[0].type)

const changeCategory = (item) => {
  if(obj.value.types ) obj.value.types.length = 0;

  subType.value = callbackTypeList[Number(item)].type;
}


const leftWidth = ref(300);
const list = ref();

const op = ref("新增");

const height = ref(1000);
const divDom = ref();

const curMain = inject('curMain')

const obj = ref({
  category: 0,
  sort: 5,
  mainid: curMain.id,
  // type: callbackTypeList[0].type,
});

const formRef = ref();

const refresh = async () => {

  const { data } = await getCallbackList(curMain.id);

  data.forEach((o) => {
    o.types = o.type.split(',');
  })
  list.value = data;
}

const newCallback = () => {
  if(op.value == '新增')return;
  op.value = '新增';
  obj.value = {
    category: 0,
    sort: 5,
    mainid: obj.value.mainid,
  }
  changeCategory(0);
}
const editCallback = (item) => {
  op.value = '修改'
  Object.assign(obj.value,item);
  // if(item.type) {
  //   obj.value.types = item.type.split(',');
  // }
}

defineExpose({
  refresh
});

const classObject = (id) => {
  return obj.value.id == id  ? "objSelected" : "";
}

const delCallback = async () => {
  if(!obj.value.id) return false;

  ElMessageBox.confirm(`是否删除该数据回调吗?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const resp = await del(obj.value.id) ;

    if(resp.code == 200) {
      ElMessage.success({ message: resp.msg });

      refresh();
      newCallback();
    }else {
      ElMessage.error({ message: resp.msg });
    }
  }).catch(()=>{});
}



const saveCallback = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      if(obj.value.types)obj.value.type = obj.value.types.join(',');

      const resp = await save(obj.value) ;

      if(resp.code == 200) {
        ElMessage.success({ message: resp.msg });

        refresh();
      }else {
        ElMessage.error({ message: resp.msg });
      }

    } catch (error) {
      console.log(error);
    }
  });
}

const filterText = ref('');

const showItem = (item) => {
  if(filterText.value == "") return true;

  if(item.remark!=null && item.remark.indexOf(filterText.value)>=0) return true;
  return false;
}


</script>

<style scoped lang="scss">
  .objSelected{
    border: 1px solid var(--el-color-primary);
  }
</style>

