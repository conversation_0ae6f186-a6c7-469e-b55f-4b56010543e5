<!-- 纵向布局 -->
<template>
  <el-container class="layout">
    <el-aside>
      <div class="aside-box" :style="{ width: isCollapse ? '65px' : '210px' }">
        <div class="logo flx-center">
          <img v-if="app.smalllogo" class="login-icon" :src="app.smalllogo" style="width:auto;max-width:80%" alt="" />
          <span style="color:#ffffff">{{app.showname}}</span>
        </div>
        <el-scrollbar>
          <el-menu
            :router="false"
            :collapse="isCollapse"
            :unique-opened="accordion"
            :collapse-transition="false"
          >
            <SubMenuPreview :menu-list="menuList" />
          </el-menu>
        </el-scrollbar>
      </div>
    </el-aside>
    <el-container>
      <el-header>
        <div class="tool-bar-lf">
          <CollapseIcon id="collapseIcon" />
        </div>
        <ToolBarRight />
      </el-header>
    </el-container>
      <el-aside style="background-color:#ffffff;border:1px solid #cdcdcd">
        <div class="aside-box" style="width:400px">
          <div class="logo flx-center" style="position: relative">
            <img v-if="app.smalllogo" class="login-icon" :src="app.smalllogo" style="width:auto;max-width:80%" alt="" />
            <span >{{app.showname}}</span>
            <div style="position:absolute; display: inline;right:10px">
              <el-button type="success" :icon="icons.Check" circle title="保存" @click="saveHandler" />
              <el-button type="primary" :icon="icons.Share" circle title="预览" @click="openHandler" />
            </div>
          </div>
          <el-scrollbar>
            <el-tree
              ref="menuRef"
              style="max-width: 400px;font-size:40px"
              :allow-drop="allowDrop"
              :allow-drag="allowDrag"
              :data="dbMenuTree"
              :highlight-current="true"
              :expand-on-click-node="false"
              :check-on-click-node="true"
              :props="treeProps"
              draggable
              default-expand-all
              node-key="menu"
              @node-drag-start="handleDragStart"
              @node-drag-enter="handleDragEnter"
              @node-drag-leave="handleDragLeave"
              @node-drag-over="handleDragOver"
              @node-drag-end="handleDragEnd"
              @node-drop="handleDrop"
            >
              <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span><el-icon v-if="data.icon"><component :is="data.icon"></component></el-icon> {{ data.title }}</span>
          <span>
            <el-tag type="primary" @click="append(data)"><el-icon><Plus></Plus></el-icon> </el-tag>
            <el-tag type="danger" @click="remove(node, data)"><el-icon><Minus></Minus></el-icon> </el-tag>
          </span>
        </span>
              </template>
            </el-tree>
          </el-scrollbar>
          <el-button type="primary" @click="append(null)"><el-icon><Plus></Plus></el-icon>添加根节点 </el-button>

        </div>
      </el-aside>
      <el-aside style="background-color:#ffffff;border:1px solid #cdcdcd">
        <div class="aside-box" style="width:300px">
          <el-form
            v-if="curMenu.title"
            ref="formRef"
            :model="curMenu"
            status-icon
            :rules="columnRules"
            :hide-required-asterisk="false"
            label-position="right"
            label-width="100"
          >
            <el-form-item label="名称" prop="name" >
              <el-input v-model="curMenu.name"></el-input>
            </el-form-item>
            <el-form-item label="标题" prop="title" >
              <el-input v-model="curMenu.title"></el-input>
            </el-form-item>
            <el-form-item label="访问路径" prop="path" >
              <el-input type="textarea" v-model="curMenu.path"></el-input>
              <el-link type="primary" @click="openDlg">选择</el-link>
            </el-form-item>
            <el-form-item label="重定向地址" prop="redirect" >
              <el-input type="textarea" v-model="curMenu.redirect"></el-input>
            </el-form-item>
<!--            <el-form-item label="页面key" prop="viewkey" >-->
<!--              <el-input v-model="curMenu.viewkey"></el-input>-->
<!--              <el-link type="primary">选择</el-link>-->
<!--            </el-form-item>-->
            <el-form-item label="组件" prop="component" >
              <el-input v-model="curMenu.component"></el-input>
            </el-form-item>
            <el-form-item label="图标" prop="icon" >
              <SelectIcon v-model="curMenu.icon" clearable ></SelectIcon>
            </el-form-item>
            <el-form-item label="高亮菜单" prop="activeMenu" >
              <el-input v-model="curMenu.activeMenu"></el-input>
            </el-form-item>
            <el-form-item label="外链地址" prop="isLink" >
              <el-input v-model="curMenu.isLink"></el-input>
            </el-form-item>
            <el-form-item label="是否隐藏" prop="isHide" >
              <el-switch v-model="curMenu.isHide"></el-switch>
            </el-form-item>
            <el-form-item label="是否全屏" prop="isFull" >
              <el-switch v-model="curMenu.isFull"></el-switch>
            </el-form-item>
            <el-form-item label="是否固定标签" prop="isAffix" >
              <el-switch v-model="curMenu.isAffix"></el-switch>
            </el-form-item>
            <el-form-item label="是否缓存" prop="isKeepAlive" >
              <el-switch v-model="curMenu.isKeepAlive"></el-switch>
            </el-form-item>


          </el-form>

        </div>
      </el-aside>
    <ViewSelectorDialog ref="viewSelectorDialogRef" @selected="selectedHandler"></ViewSelectorDialog>


  </el-container>
</template>

<script setup lang="ts" name="layoutVertical">
import {ref, computed, onMounted, reactive } from "vue";
import { useRoute } from "vue-router";
import { useAuthDevStore } from "@/stores/modules/authDev";
import { useGlobalStore } from "@/stores/modules/global";
import Main from "@/layouts/components/Main/index.vue";
import ToolBarLeft from "@/layouts/components/Header/ToolBarLeft.vue";
import ToolBarRight from "@/layouts/components/Header/ToolBarRight.vue";
import SubMenuPreview from "@/layouts/components/Menu/SubMenuPreview.vue";
import logo from "@/assets/images/logo.svg?url"
import {ElMessage} from "element-plus";
import ViewSelectorDialog from "./ViewSelectorDialog.vue";

import type Node from 'element-plus/es/components/tree/src/model/node'
import type { DragEvents } from 'element-plus/es/components/tree/src/model/useDragNode'
import type {
  AllowDropType,
  NodeDropType,
} from 'element-plus/es/components/tree/src/tree.type'

import { getAppMenu , saveAppMenu, getApp } from '@/api/dev/app'
import * as icons from "@element-plus/icons-vue";
import SelectIcon from "@/components/SelectIcon/index.vue";
import CollapseIcon from "@/layouts/components/Header/components/CollapseIcon.vue";

const route = useRoute();
const authStore = useAuthDevStore();
const globalStore = useGlobalStore();
const accordion = computed(() => globalStore.accordion);
const isCollapse = computed(() => globalStore.isCollapse);
const menuList = computed(() => {
  let list = [];
  dbMenuTree.value.forEach( (m) => {
    deepMenu(list,m);
  })
  return list;
});

const deepMenu = (list , m) => {
  let newmenu = {
    "path": m.path || (m.viewkey?'/menuview/:key':null),
    "name": m.name ||  m.viewkey || '',
    "redirect": m.redirect,
    "params" : m.viewkey? {key:m.viewkey} : null,
    "meta": {
      "icon": m.icon,
      "title": m.title,
      "isLink": m.isLink,
      "isHide": m.isHide,
      "isFull": m.isFull,
      "isAffix": m.isAffix,
      "isKeepAlive": ('isKeepAlive' in m)? m.isKeepAlive:true
    },
    children: []
  }

  list.push(newmenu)
  if(m.children && m.children.length>0) {
    m.children.forEach((item) => {
      deepMenu(newmenu.children,item)
    })
  }
}

const treeProps = ref({label:'title'})

const handleDragStart = (node: Node, ev: DragEvents) => {
  console.log('drag start', node)
}
const handleDragEnter = (
  draggingNode: Node,
  dropNode: Node,
  ev: DragEvents
) => {
  console.log('tree drag enter:', dropNode.title)
}
const handleDragLeave = (
  draggingNode: Node,
  dropNode: Node,
  ev: DragEvents
) => {
  console.log('tree drag leave:', dropNode.title)
}
const handleDragOver = (draggingNode: Node, dropNode: Node, ev: DragEvents) => {
  console.log('tree drag over:', dropNode.title)
}
const handleDragEnd = (
  draggingNode: Node,
  dropNode: Node,
  dropType: NodeDropType,
  ev: DragEvents
) => {
  console.log('tree drag end:', dropNode && dropNode.title, dropType)
}
const handleDrop = (
  draggingNode: Node,
  dropNode: Node,
  dropType: NodeDropType,
  ev: DragEvents
) => {
  console.log('tree drop:', dropNode.title, dropType)
}
const allowDrop = (draggingNode: Node, dropNode: Node, type: AllowDropType) => {
  if (dropNode.data.title === 'Level two 3-1') {
    return type !== 'inner'
  } else {
    return true
  }
}
const allowDrag = (draggingNode: Node) => {
  return !draggingNode.data.title.includes('Level three 3-1-1')
}

const dbMenuTree = ref<Tree[]>( []  );

const app = ref({});

onMounted( async ()=>{
  const resp = await getApp(route.params.appid)
  Object.assign(app.value,resp.data);

  const { data } = await getAppMenu(route.params.appid);
  dbMenuTree.value = data;
})

interface Tree {
  id: number
  path: string
  name: string
  title: string
  icon: string
  children?: Tree[]
}
let id = 100

const append = (data: Tree) => {
  const newChild = { title: '菜单' + id, name: 'menu' + id, path: '/menu' + id, isKeepAlive: true, children: [] };
  id++;
  if(!data) {
    dbMenuTree.value.push(newChild)
  }else {
    if (!data.children) {
      data.children = []
    }
    data.children.push(newChild)
  }
  // dataSource.value = [...dataSource.value]

}

const remove = (node: Node, data: Tree) => {
  const parent = node.parent
  const children: Tree[] = parent.data.children || parent.data
  const index = children.findIndex((d) => d.id === data.id)
  children.splice(index, 1)
  dbMenuTree.value = [...dbMenuTree.value]
}

const curMenu = computed(()=> {
  return menuRef.value?.getCurrentNode() || {};
});
const menuRef = ref();

const columnRules = reactive({
  name: [{ required: true, message: "请输入菜单名称" }],
  title: [{ required: true, message: "请输入菜单标题" }],
});



const saveHandler = async () => {
  console.log(dbMenuTree.value)
  const resp = await saveAppMenu(route.params.appid,dbMenuTree.value)

  if(resp.code===200) {
    ElMessage.success(resp.msg);
  }else {
    ElMessage.error(resp.msg);
  }

}


const openHandler = () => {
  open('/' + app.value.name);
}

const viewSelectorDialogRef = ref();

const openDlg = () => {
  viewSelectorDialogRef.value?.openDialog();
}

const selectedHandler = (param) => {
  console.log("selectedHandler")
  console.log(param)
  console.log(curMenu.value)
  curMenu.value.path = '/menuview/' + param.hashkey;
  curMenu.value.title = param.title;
}

</script>

<style scoped lang="scss">
@import "./index.scss";
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.vertical .el-menu-item.is-active::before, .classic .el-menu-item.is-active::before, .transverse .el-menu-item.is-active::before {
  left: 0;
}
.el-menu-item.is-active::before {
  left:0 !important;
}
.vertical,
.classic,
.transverse {
  .el-menu-item {
    &.is-active {
      &::before {
        left: 0;
      }
    }
  }
}

.tool-bar-lf {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  white-space: nowrap;
}

</style>
