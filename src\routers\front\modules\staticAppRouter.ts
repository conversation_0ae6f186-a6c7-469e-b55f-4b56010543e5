import { RouteRecordRaw } from "vue-router";
import { HOME_URL, LOGIN_URL } from "@/config";

/**
 * staticDevRouter (静态路由)
 */
export const staticAppRouter: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: "/index"
  },
  {
    path: LOGIN_URL,
    name: "login",
    component: () => import("@/views/front/login/index.vue"),
    meta: {
      title: "登录"
    }
  },
  {
    path: "/view/:key",
    name: "view",
    component: () => import("@/views/front/Preview.vue"),
    meta: {
      title: "视图预览"
    }
  },
  {
    path: "/menuview/:key",
    name: "menuview",
    component: () => import("@/views/front/Preview.vue"),
    meta: {
      title: "视图预览"
    }
  },
  {
    path: "/layout",
    name: "layout",
    // component: () => import("@/layouts/index.vue"),
    component: () => import("@/layouts/indexAsync.vue"),
    redirect: HOME_URL,
    children: []
  }
];

/**
 * errorRouter (错误页面路由)
 */
export const errorRouter = [
  {
    path: "/403",
    name: "403",
    component: () => import("@/components/ErrorMessage/403.vue"),
    meta: {
      title: "403页面"
    }
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/components/ErrorMessage/404.vue"),
    meta: {
      title: "404页面"
    }
  },
  {
    path: "/500",
    name: "500",
    component: () => import("@/components/ErrorMessage/500.vue"),
    meta: {
      title: "500页面"
    }
  },
  // Resolve refresh page, route warnings
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/components/ErrorMessage/404.vue")
  }
];
