import { defineStore } from "pinia";
import { UserState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";

export const useUserStore = () => {
  const key = "zerocode_dev_user";
  return defineStore({
    id: key,
    state: (): UserState => ({
      // token: "",
      _zreport_dev_token: "",
      _zreport_user_token: "",
      devInfo: {},
      userInfo: {},
      app: {},
    }),
    getters: {},
    actions: {
      // Set Token
      setTokenDev(token: string) {
        this._zreport_dev_token = token;
      },
      setTokenUser(token: string) {
        this._zreport_user_token = token;
      },
      // Set setUserInfo
      setDevInfo(devInfo: UserState["devInfo"]) {
        this.devInfo = devInfo;
      },
      setUserInfo(userInfo: UserState["userInfo"]) {
        this.devInfo = userInfo;
      },
      setApp(app ) {
        this.app = app;
      }
    },
    persist: piniaPersistConfig(key)
  })();
}
