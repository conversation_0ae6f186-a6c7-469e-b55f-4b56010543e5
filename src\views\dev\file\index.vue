<template>
  <div class="main-box">
    <div class="table-box">
      <ProTable
        ref="proTable"
        row-key="id"
        :indent="20"
        stripe
        :columns="columns"
        :request-api="getFileList"
        :init-param="initParam"
        :pagination="false"
        :toolButton="false"
        @sort-change="sortFunction"
        :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeaderLf >
          <el-text type="primary" v-if="navMenu.isRoot">根目录</el-text>
          <template v-else>
            <el-link type="primary" @click="nav(navMenu.upPath)">返回上一级</el-link>
            &nbsp;&nbsp;
            <template v-for="item in navMenu.subPath">
              <el-link type="success" @click="nav(item.path)">{{item.name}}</el-link>
              &gt;
            </template>
            <el-text >{{navMenu.last}}</el-text>

          </template>
        </template>
        <template #tableHeader >

          <el-button type="primary" :icon="icons.Plus" @click="mkdirHandler()" style="margin-left:100px">创建子目录</el-button>
          <el-dropdown style="margin: 0 10px;">
            <el-button type="success" :icon="icons.Plus">
              创建文件<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="item in createFileTypes" @click="createFileHandle(item)">{{item}}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="success" plain :icon="icons.Upload" @click="openUploadDialog">上传</el-button>
          <el-tooltip content="把整个根目录打包ZIP，并下载" placement="top">
              <el-button type="success" plain :icon="icons.Download"  >
                <el-link  type="success" href="/dev/file/zip" target="_blank">打包下载</el-link>
              </el-button>
          </el-tooltip>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button type="primary" link :icon="icons.Check" @click="openHandler(scope.row)">打开</el-button>
          <el-button type="primary" link :icon="icons.Edit" @click="renameHandler( scope.row)">重命名</el-button>
          <el-button type="primary" link :icon="icons.Delete" @click="delHandler(scope.row)">删除</el-button>
        </template>
      </ProTable>
    </div>
    <CodeEditor  ref="codeEditorRef"  @submit="codeSubmit"
                 :title="code.title"
                 :type="code.type"
    ></CodeEditor>

    <UploadDialog ref="uploadDialogRef" @uploaded="uploadedHandler"></UploadDialog>
  </div>
</template>

<script setup lang="tsx" name="dsIndex">
import {onMounted, reactive, ref, watch} from "vue";
import {ElMessage, ElMessageBox, ElNotification} from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import * as icons from "@element-plus/icons-vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import {getFileList, saveFile, mkdir, createFile, renameFile, delFileDef, uploadFile } from "@/api/dev/file";
import CodeEditor from "@/views/dashboard/editor/setting/ComponentSetting/CodeEditor.vue";
import UploadDialog from "./UploadDialog.vue";
import dcRequest from "axios";
import {toJson} from "@/utils";

// ProTable 实例
const proTable = ref<ProTableInstance>();

const code = ref({
  title: '',
  type: '',
  path: '',
})

const codeEditorRef = ref();


onMounted(() => {

  // proTable.value!.pageable.pageSize = 15;
  // proTable.value!.pageable.pageSizes = [15,30,50];
});

const createFileTypes = ['html','js','css','json','txt'];

const initParam = ref({
  path: "/"
})

const navMenu = ref({});

watch(
  () => initParam.value.path,
  (path: any) => {
    console.log(path)
    navMenu.value = {};
    if(path === '/') {
      navMenu.value.isRoot = true;
      return;
    }
    navMenu.value.subPath = [{
      name: '根目录',
      path: '/'
    }];

    var s = 1;
    var e = -1;
    while(true) {
      e = path.indexOf('/',s);
      if(e>0) {
        navMenu.value.subPath.push({
          name: path.substr(s,e-1),
          path: path.substr(0,e)
        })
      }else {
        navMenu.value.last = path.substr(s)
        break;
      }
      s = e + 1;
    }
    var l = navMenu.value.subPath.length;
    navMenu.value.upPath = navMenu.value.subPath[l-1].path;

    console.log(navMenu.value)
  },
  { immediate: true }
);


// 表格配置项
const columns = reactive([
  { type: "index", label: "#", width: 50 },
  { prop: "name", label: "文件名",align:"left", sortable:'custom', search: { el: "input" },
    render: scope => {
      return (
        <>
        {scope.row.directory ? (
          <el-icon size="22" color="#ffd144"> <Folder></Folder></el-icon>
        ) : (
          <el-icon size="22" color="#40a8f4"> <Document></Document> </el-icon>
        )}
          &nbsp;<el-link type="primary" style="margin-top:-15px" onClick={() => editFileHandler(scope.row)}>{scope.row.name}</el-link>
        </>
      )
    }
  },
  { prop: "path", label: "相对地址",align:"left",sortable:false,minWidth:200,
    render: scope => {
      scope.row.path = scope.row.path.replaceAll("\\","/");
      return (
        <>
          <el-button size="small" type="success" onClick={() => copyHandler(scope.row)}>复制</el-button>&nbsp;
          { scope.row.path}
        </>
      )
    }

  },
  { prop: "size", label: "大小",align:"right", width: 100,sortable:'custom' },
  { prop: "lastModi", label: "修改时间", width: 160,sortable:'custom' },
  { prop: "operation", label: "操作", width: 250, fixed: "right" }
]);


const mkdirHandler = () => {
  ElMessageBox.prompt('请输入您要创建的目录名称', '温馨提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /[^\s]/,
    inputErrorMessage: '请输入目录名称',
  }).then(async ({value}) => {
    const resp = await mkdir({path: initParam.value.path, name: value});

    if (resp.code == 200) {
      ElMessage.success({message: resp.msg});

      proTable.value?.search();

    } else {
      ElMessage.error({message: resp.msg});
    }
  })
}

const delHandler = (param) => {
  console.log(param);
  ElMessageBox.confirm('请确认是否要删除该文件/', '温馨提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /[^\s]/,
    inputErrorMessage: '请输入目录名称',
  }).then(async ({value}) => {
    const resp = await delFileDef({path: initParam.value.path, name: param.name});

    if (resp.code == 200) {
      ElMessage.success({message: resp.msg});

      proTable.value?.search();

    } else {
      ElMessage.error({message: resp.msg});
    }
  })

}
const renameHandler = (param) => {
  console.log(param);
  ElMessageBox.prompt('原名称：' + param.name + '    请输入新的名称：', '温馨提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /[^\s]/,
    inputErrorMessage: '请输入新的名称',
  }).then(async ({value}) => {
    const resp = await renameFile({path: initParam.value.path, name: param.name, newname: value});

    if (resp.code == 200) {
      ElMessage.success({message: resp.msg});

      proTable.value?.search();

    } else {
      ElMessage.error({message: resp.msg});
    }
  })

}

const createFileHandle = (type) => {
  ElMessageBox.prompt('请输入您要创建的文件名称', '温馨提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /[^\s]/,
    inputErrorMessage: '请输入文件名称',
  }).then(async ({value}) => {
    const resp = await createFile({path: initParam.value.path, name: value + '.' + type});

    if (resp.code == 200) {
      ElMessage.success({message: resp.msg});

      proTable.value?.search();

    } else {
      ElMessage.error({message: resp.msg});
    }
  })
}

const copyHandler = async (param) => {
  await navigator.clipboard.writeText(param.path);
  ElMessage.success('已复制到剪切板：' + param.path)
}

const txtTypes = ['htm', 'html', 'css', 'js', 'xml', 'json', 'txt']

const basepath = import.meta.env.VITE_API_URL;


const editFileHandler = async (param) => {
  if(param.directory) {
    nav(initParam.value.path + (initParam.value.path =="/"?'':'/') + param.name )
    return;
  }

  var i = param.name.lastIndexOf(".");
  var ext;
  if (i >= 0) {
    ext = param.name.substr(i + 1);
  }
  if (ext && txtTypes.indexOf(ext) >= 0) {
    Object.assign(code.value, {
      type: ext,
      title: param.name,
      path: param.path,
    })

    const res = await dcRequest.get( basepath + param.path + "?__t=" + (new Date()).getTime());
    codeEditorRef.value?.openDialog(res.data);

  } else {
    open(basepath + param.path)
  }
}


const openHandler = async (param) => {
  if(param.directory) {
    nav(initParam.value.path + (initParam.value.path =="/"?'':'/') + param.name )
    return;
  }

  var i = param.name.lastIndexOf(".");
  var ext;
  if (i >= 0) {
    ext = param.name.substr(i + 1);
  }

  open(basepath + param.path)
}



const nav = (path) => {
  initParam.value.path = path;
  proTable.value?.search();
}



const codeSubmit = async (type,data) => {

  var path = code.value.path;
  var i = path.indexOf("/",10);
  path = path.substr(i);

  const resp = await saveFile({path: path, content: data});

  if (resp.code == 200) {
    ElMessage.success({message: resp.msg});
  } else {
    ElMessage.error({message: resp.msg});
  }

}

const uploadDialogRef = ref();

const openUploadDialog = () => {
  uploadDialogRef.value?.openDialog({path:initParam.value.path});
}

const uploadedHandler = () => {
  proTable.value?.search();
}

const sortFunction = (data: {column: any, prop: string, order: any }) => {
  console.log(data);
  initParam.value.orderColumn = data.prop;
  initParam.value.orderSort = data.order;
  proTable.value?.search();

}

</script>
<style scoped lang="scss">
  .dsSelected{
    border: 1px solid var(--el-color-primary);
  }
</style>

