<template>
  <v-form-designer ref="vfDesigner" :field-list-api="fieldListApi" :banned-widgets="testBanned"
                   :designer-config="designerConfig" :json="jsonObj">
    <!-- 自定义按钮插槽演示 -->
    <template #customToolButtons>
      <el-button link type="primary" @click="saveFormJson">
        <el-icon><DocumentChecked></DocumentChecked></el-icon>&nbsp;保存
      </el-button>
      <el-button link type="primary" @click="openForm">
        <el-icon><Share></Share></el-icon>&nbsp;打开
      </el-button>
    </template>
  </v-form-designer>

</template>
<script setup >
import { ref, reactive } from 'vue';
import {saveView} from '@/api/dev/view.ts';
import '@/../lib/vform1/designer.style.css'  //引入VForm3样式
import { MessageUtil } from '@/utils/messageUtil.ts';
import { useUserStore } from "@/stores/modules/user";

const userStore = useUserStore();

const props = defineProps({
  id: Number,
  mainid: Number,
  hashkey: String,
  type : String,
  title: String,
  fit: String,
  width: Number,
  json: String,
})

const jsonObj = ref(props.json);

const vfDesigner = ref(null)
const fieldListApi = reactive({
  URL:  `${import.meta.env.VITE_API_URL}dev/column/getShortColumnListNoPk?mid=${props.mainid}` ,
  labelKey: 'showname',
  nameKey: 'name' ,
  headers : {
    '_zreport_dev_token' : userStore._zreport_dev_token
  }
})


const testBanned = ref([
  //'sub-form',
  //'alert',
])

const designerConfig = reactive({
  languageMenu: true,
  resetFormJson: true,  //表单设计器初始化自动清空内容
  //externalLink: false,
  //formTemplates: false,
  //eventCollapse: false,
  //clearDesignerButton: false,
  //previewFormButton: false,

  //presetCssCode: '.abc { font-size: 16px; }',
})

const saveFormJson = async () => {
  let formJson = vfDesigner.value.getFormJson()
  console.log(formJson);

  const obj = {
    id : props.id,
    mainid: props.mainid,
    hashkey: props.hashkey,
    json: JSON.stringify(formJson),
  }

  const resp = await saveView(obj);
  MessageUtil.dealresp(resp);

}

const openForm = () => {
  open('#/view/' + props.hashkey)
}


</script>

