<template>
  <div class="text-component" :style="textStyle">
    <div 
      v-if="config.type === 'title'"
      class="text-title"
      :style="titleStyle"
    >
      {{ config.content || '标题文本' }}
    </div>
    <div 
      v-else-if="config.type === 'subtitle'"
      class="text-subtitle"
      :style="subtitleStyle"
    >
      {{ config.content || '副标题文本' }}
    </div>
    <div 
      v-else-if="config.type === 'paragraph'"
      class="text-paragraph"
      :style="paragraphStyle"
    >
      {{ config.content || '这是一段描述文本，可以用来展示详细信息或说明内容。' }}
    </div>
    <div 
      v-else-if="config.type === 'label'"
      class="text-label"
      :style="labelStyle"
    >
      {{ config.content || '标签文本' }}
    </div>
    <div 
      v-else
      class="text-normal"
      :style="normalStyle"
    >
      {{ config.content || '普通文本' }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  config?: {
    type?: 'title' | 'subtitle' | 'paragraph' | 'label' | 'normal';
    content?: string;
    fontSize?: string;
    fontWeight?: string;
    color?: string;
    textAlign?: 'left' | 'center' | 'right';
    lineHeight?: string;
    backgroundColor?: string;
    padding?: string;
    borderRadius?: string;
    border?: string;
  };
  style?: any;
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({
    type: 'normal',
    content: '文本内容',
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#ffffff',
    textAlign: 'left',
    lineHeight: '1.5',
    backgroundColor: 'transparent',
    padding: '10px',
    borderRadius: '4px',
    border: 'none'
  })
});

const textStyle = computed(() => ({
  backgroundColor: props.config?.backgroundColor || 'transparent',
  padding: props.config?.padding || '10px',
  borderRadius: props.config?.borderRadius || '4px',
  border: props.config?.border || 'none',
  textAlign: props.config?.textAlign || 'left',
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  ...props.style
}));

const titleStyle = computed(() => ({
  fontSize: props.config?.fontSize || '24px',
  fontWeight: props.config?.fontWeight || 'bold',
  color: props.config?.color || '#ffffff',
  lineHeight: props.config?.lineHeight || '1.2',
  margin: 0
}));

const subtitleStyle = computed(() => ({
  fontSize: props.config?.fontSize || '18px',
  fontWeight: props.config?.fontWeight || '600',
  color: props.config?.color || '#ffffff',
  lineHeight: props.config?.lineHeight || '1.3',
  margin: 0
}));

const paragraphStyle = computed(() => ({
  fontSize: props.config?.fontSize || '14px',
  fontWeight: props.config?.fontWeight || 'normal',
  color: props.config?.color || 'rgba(255, 255, 255, 0.8)',
  lineHeight: props.config?.lineHeight || '1.6',
  margin: 0
}));

const labelStyle = computed(() => ({
  fontSize: props.config?.fontSize || '12px',
  fontWeight: props.config?.fontWeight || '500',
  color: props.config?.color || 'rgba(255, 255, 255, 0.7)',
  lineHeight: props.config?.lineHeight || '1.4',
  margin: 0,
  textTransform: 'uppercase',
  letterSpacing: '0.5px'
}));

const normalStyle = computed(() => ({
  fontSize: props.config?.fontSize || '14px',
  fontWeight: props.config?.fontWeight || 'normal',
  color: props.config?.color || '#ffffff',
  lineHeight: props.config?.lineHeight || '1.5',
  margin: 0
}));
</script>

<style lang="scss" scoped>
.text-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  word-wrap: break-word;
  word-break: break-all;

  .text-title,
  .text-subtitle,
  .text-paragraph,
  .text-label,
  .text-normal {
    width: 100%;
    white-space: pre-wrap;
  }

  .text-title {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  }

  .text-subtitle {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  }

  .text-paragraph {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  }

  .text-label {
    font-family: 'Roboto', 'Arial', sans-serif;
  }

  .text-normal {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  }
}
</style>
