<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择视图"
    :destroy-on-close="true"
    top="5vh"
    width="90%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    draggable
  >

    <div class="table-box">
      <ProTable
        ref="proTable"
        row-key="id"
        :indent="20"
        stripe
        :columns="columns"
        :tool-button="false"
        :pagination="true"
        :request-api="getSelectedViewList"
        @row-dblclick="rowDblClick"
        :request-auto="false"
        :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
      >
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button type="primary" link :icon="icons.Check" @click="selectRow(scope.row)" >选择该视图</el-button>
        </template>
      </ProTable>
    </div>
  </el-dialog>
</template>

<script setup lang="ts" name="ViewSelectorDialog">
import {defineEmits, onMounted, reactive, ref} from "vue";
import { getSelectedViewList } from "@/api/dev/view";
import ProTable from "@/components/ProTable/index.vue";
import {ProTableInstance} from "@/components/ProTable/interface";
import * as icons from "@element-plus/icons-vue";


// ProTable 实例
const proTable = ref<ProTableInstance>();

onMounted(() => {

  // proTable.value!.pageable = false;
  // proTable.value!.pageable.pageSizes = [15,30,50];
  // proTable.value!.pageable.pageSize = 15;
  // proTable.value!.search();
});





const columns = reactive([
  { type: "index", label: "#", width: 80 },
  { prop: "type", label: "业务类型",sortable:true,
    tag: true,
    enum: [
      {label: "BI报表",value: "bi", tagType:"success"},
      {label: "MIS页面",value: "ZGrid", tagType:"primary"},
    ],
    search: { el: "select" },
  },
  { prop: "hashkey", label: "Hashkey",sortable:true  },
  { prop: "title", label: "标题",sortable:true, search: { el: "input" }  },
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]);


const emit = defineEmits(['selected'])

const rowDblClick = (row) => {
  selectRow(row);
}

const selectRow = (row) => {
  console.log(row)

  dialogVisible.value = false ;
  emit('selected',row);
  // dialogVisible.value = false;
}

const dialogVisible = ref(false);

const openDialog = () => {
  dialogVisible.value = true;

  setTimeout( () => {
    Object.assign(proTable.value!.pageable,{
      pageSizes : [15,30,50],
      pageSize : 15,
      pageNum : 1,
    })
    proTable.value!.search();
  },10)

}

defineExpose({
  openDialog
});
</script>
