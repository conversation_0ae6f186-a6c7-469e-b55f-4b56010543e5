<template>
  <monaco-editor v-model="code"
                 :read-only="disabled"
                 :language="language"
                 :options="options"
                 :height="height"></monaco-editor>

</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import MonacoEditor from '@/components/MonacoEditor/index'

interface Props {
  language: string;
  disabled: boolean;
  height: string | number;
  options: {
    fontSize:number;
  };
  value: string | object | [];
}

const props = withDefaults(defineProps<Props>(), {
  language: "javascript",
  disabled: false,
  height: 400,
  options: {
    fontSize: 14,
  }
});

const code = ref();

watch(code ,async (newCode, oldCode) => {

})

</script>

<style scoped lang="scss">

</style>
