# 🎨 数据大屏设计器演示指南

## 🚀 功能演示

### 1. 界面布局
- **左侧组件库**: 深色主题，小图标设计，3列网格布局
- **中间画布**: 集成vue3-sketch-ruler标尺，支持精确定位
- **右侧属性面板**: 深色主题，实时配置组件属性

### 2. 拖拽操作演示

#### 步骤1: 拖拽组件到画布
1. 从左侧组件库选择"简单图表"
2. 拖拽到中间画布区域
3. 组件会自动添加到画布上，显示默认的柱状图

#### 步骤2: 调整组件位置和大小
1. 点击画布上的组件进行选择
2. 拖拽组件四角调整大小
3. 拖拽组件中心移动位置
4. 使用标尺进行精确定位

#### 步骤3: 配置组件属性
1. 选中组件后，右侧显示属性面板
2. 在"基础属性"标签页修改组件名称、尺寸
3. 在"样式配置"标签页修改背景色、边框等
4. 在"数据配置"标签页设置数据源

### 3. 组件类型演示

#### 图表组件
- **简单图表**: 演示用柱状图，支持数据配置
- **实时访问图**: 折线图展示访问趋势
- **饼图**: 展示比例数据
- **地图**: 中国地图展示地域数据

#### 数据组件
- **数据卡片**: KPI指标展示，支持趋势显示
- **数据表格**: 排行榜样式，支持趋势图标
- **数据列表**: 列表形式展示数据

#### 装饰组件
- **文本组件**: 支持标题、副标题、段落等多种类型
- **边框**: 装饰性边框元素
- **背景**: 背景装饰元素

### 4. 标尺功能演示

#### vue3-sketch-ruler 特性
- **精确定位**: 像素级精确定位
- **辅助线**: 拖拽添加水平/垂直辅助线
- **缩放支持**: 支持画布缩放操作
- **深色主题**: 与整体设计风格一致

#### 操作方法
- 点击标尺添加辅助线
- 拖拽辅助线调整位置
- 双击辅助线删除
- 使用缩放按钮调整画布比例

### 5. 预览模式演示

#### 切换预览
1. 点击顶部工具栏"预览"按钮
2. 进入全屏预览模式
3. 查看实际大屏效果
4. 点击"退出预览"返回设计模式

#### 响应式适配
- 自动适配不同屏幕尺寸
- 保持组件比例和布局
- 支持1920x1080标准大屏分辨率

### 6. 保存和导出演示

#### 本地保存
1. 点击"保存"按钮
2. 设计数据保存到localStorage
3. 下次打开自动加载保存的设计

#### 导出配置
1. 点击"导出"按钮
2. 下载JSON配置文件
3. 包含完整的布局和组件配置信息

### 7. 深色主题特色

#### 组件库优化
- 3列网格布局，更紧凑
- 小图标设计，更清晰
- 深色背景，护眼舒适
- 悬停效果，交互友好

#### 属性面板优化
- 深色主题表单控件
- 清晰的标签和输入框
- 统一的颜色方案
- 良好的对比度

### 8. 技术亮点

#### 现代化技术栈
- Vue 3 + TypeScript
- Element Plus UI组件库
- vue3-sketch-ruler 标尺组件
- vue-grid-layout 网格布局
- SCSS 样式预处理

#### 性能优化
- 组件懒加载
- 响应式数据绑定
- 高效的拖拽算法
- 流畅的动画效果

## 🎯 使用建议

1. **开始设计**: 从简单组件开始，逐步添加复杂组件
2. **布局规划**: 使用标尺和辅助线进行精确布局
3. **样式统一**: 保持组件样式的一致性
4. **数据配置**: 根据实际需求配置数据源
5. **预览测试**: 经常切换到预览模式查看效果
6. **及时保存**: 定期保存设计避免丢失

## 🔧 扩展功能

### 即将支持
- 更多图表类型
- 动画效果配置
- 数据源管理
- 模板系统
- 协作功能

### 自定义开发
- 添加新的组件类型
- 扩展属性配置选项
- 集成第三方图表库
- 开发专用数据适配器

---

**开始体验数据大屏设计器的强大功能吧！** 🎉
