import router from "@/routers/dev";
import { defineStore } from "pinia";
import { getUrlWithParams } from "@/utils";
import { useKeepAliveStore } from "./keepAlive";
import { TabsState, TabsMenuProps } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";

const keepAliveStore = useKeepAliveStore();

export const useTabsStore = () => {
  const key = (!!router.appname)? "zerocode_app_tabs_" + router.appname: "zerocode_dev_tabs";
  console.log(key)
  return defineStore({
    id: key,
    state: (): TabsState => ({
      tabsMenuList: []
    }),
    actions: {
      // Add Tabs
      async addTabs(tabItem: TabsMenuProps) {
        if (this.tabsMenuList.every(item => item.path !== tabItem.path)) {
          this.tabsMenuList.push(tabItem);
        }
        // add keepalive
        if (!keepAliveStore.keepAliveName.includes(tabItem.name) && tabItem.isKeepAlive) {
          keepAliveStore.addKeepAliveName(tabItem.name);
        }
      },
      // Remove Tabs
      async removeTabs(tabPath: string, isCurrent: boolean = true) {
        if (isCurrent) {
          this.tabsMenuList.forEach((item, index) => {
            if (item.path !== tabPath) return;
            const nextTab = this.tabsMenuList[index + 1] || this.tabsMenuList[index - 1];
            if (!nextTab) return;
            router.push(nextTab.path);
          });
        }
        // remove keepalive
        const tabItem = this.tabsMenuList.find(item => item.path === tabPath);
        tabItem?.isKeepAlive && keepAliveStore.removeKeepAliveName(tabItem.name);
        // set tabs
        this.tabsMenuList = this.tabsMenuList.filter(item => item.path !== tabPath);
      },
      // Close Tabs On Side
      async closeTabsOnSide(path: string, type: "left" | "right") {
        const currentIndex = this.tabsMenuList.findIndex(item => item.path === path);
        if (currentIndex !== -1) {
          const range = type === "left" ? [0, currentIndex] : [currentIndex + 1, this.tabsMenuList.length];
          this.tabsMenuList = this.tabsMenuList.filter((item, index) => {
            return index < range[0] || index >= range[1] || !item.close;
          });
        }
        // set keepalive
        const KeepAliveList = this.tabsMenuList.filter(item => item.isKeepAlive);
        keepAliveStore.setKeepAliveName(KeepAliveList.map(item => item.name));
      },
      // Close MultipleTab
      async closeMultipleTab(tabsMenuValue?: string) {
        this.tabsMenuList = this.tabsMenuList.filter(item => {
          return item.path === tabsMenuValue || !item.close;
        });
        // set keepalive
        const KeepAliveList = this.tabsMenuList.filter(item => item.isKeepAlive);
        keepAliveStore.setKeepAliveName(KeepAliveList.map(item => item.name));
      },
      // Set Tabs
      async setTabs(tabsMenuList: TabsMenuProps[]) {
        this.tabsMenuList = tabsMenuList;
      },
      // Set Tabs Title
      async setTabsTitle(title: string) {
        const url = getUrlWithParams()
        for(var i=0;i<this.tabsMenuList.length;i++) {
          if (this.tabsMenuList[i].path == url) {
            this.tabsMenuList[i].title = title;
            return;
          }
        }
      },
      async setTabsIcon(icon: string) {
        const url = getUrlWithParams()
        for(var i=0;i<this.tabsMenuList.length;i++) {
          if (this.tabsMenuList[i].path == url) {
            this.tabsMenuList[i].icon = icon;
            return;
          }
        }
      }
    },
    persist: piniaPersistConfig(key)
  })();
}
