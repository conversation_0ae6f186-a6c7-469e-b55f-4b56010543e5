<template>
  <div class="container">
    <!-- 自定义布局的部分 -->
    <div class="grid-box" >
      <grid-layout
        ref="gridLayout"
        v-model:layout="layout"
        :col-num="48"
        :row-height="20"
        :is-draggable="true"
        :is-resizable="true"
        :is-mirrored="false"
        :vertical-compact="true"
        :prevent-collision="true"
        :margin="[10, 10]"
        :use-css-transforms="true"
        :responsive="true"
        @layout-updated="layoutUpdatedEvent"
      >
        <grid-item ref="gridItem" @resized="resizedEvent" v-for="item in layout" :x="item.x" :y="item.y" :w="item.w" :h="item.h" :i="item.i" :key="item.i">
<!--          <span class=" close" @click="delItem(item)"><i class="sky-iconfont icon-guanbi"></i></span>-->
          <span class=" close" @click="delItem(item)" >x</span>
          <keep-alive>
            <component :is="item.name" />
          </keep-alive>
        </grid-item>
      </grid-layout>
    </div>
    <!-- 可拖入的组件部分 -->
    <div class="components-box">
      <div class="ctrl-box" v-for="item in componentsInfo" :key="item.id" @drag="drag" @dragstart="dragstart($event, item)" @dragend="dragend" draggable="true">
        {{item.des}}
      </div>
      <el-button @click="loadA()">加载</el-button>
      <el-button @click="printInfo()">测试</el-button>
      <el-button @click="changeCom()">换组件</el-button>
      <component :is="aaa" shallowRef="comRef"/>
<!--      <component :is="waterChart" shallowRef="comRef"/>-->
    </div>
  </div>
</template>

<script setup >
  import { onMounted, ref, reactive, getCurrentInstance, defineAsyncComponent, markRaw } from 'vue'
  // import { about } from '@/views/about/index'

  import * as icons from "@element-plus/icons-vue";

  // import  about  from '@/views/about/index.vue';
  // import lineChart  from '@/views/echarts/lineChart/index.vue';
  // import waterChart from '@/views/echarts/waterChart/index.vue';

  // import { BarChart, LineChart, LinesChart, PieChart, ScatterChart, RadarChart, GaugeChart } from "@/components/echarts/charts";


  const { proxy } = getCurrentInstance()
  const layout = ref([])
  const colNum = 48
  let defaultH = 1
  let defaultW = 1
  let mouseXY = {
    x: null,
    y: null
  }
  let DragPos = {
    x: null,
    y: null,
    w: null,
    h: null,
    i: null
  }
  const componentsInfo = ref([]);

  let aaa = ref("el-input")

  let currentDragCom = null
  onMounted(() => {
    document.addEventListener('dragover', (e) => {
      e.preventDefault()
      mouseXY.x = e.clientX;
      mouseXY.y = e.clientY;
    }, false);
    document.addEventListener('dragenter', function (event) {
      // 阻止默认行为
      event.preventDefault()
    });
    processLayout(layout.value)
    // Vue.component('runoob', { template: '<h1>自定义组件!</h1>' })

    // Object.keys(coms).map(ele => {
    //   let component = coms[ele];
    //   Vue.component(component.name, component);
    // });


    setTimeout(function(){
      loadAllComponent();
    },100)

    // loadAllComponent();
  })
  // 处理布局数据中的组件
  const processLayout = (layoutSetInfo) => {
    for (let i = 0; i < layoutSetInfo.length; i++) {
      let item = layoutSetInfo[i]
      if (!item.component) {
        continue
      }
      let resComp = loadComponent(item.component)
      item.loadComp = resComp
    }
  }

  const layoutUpdatedEvent = (newLayout) => {
    console.log("Updated layout: ", newLayout)
  }

  const printInfo = () => {
    console.log(compMap)
    console.log(layout)
    console.log(layout.value[0].loadComp)
  }

  const compMap = ref( new Map() );

  const changeCom = () => {
    aaa.value = "about";
  }

  const componentName = ref(null);
  // 引入组件
  const loadComponent = async (com) => {
    if(!componentName) {
      const module = await import(`@/views/about/index.vue`);
      componentName.value = module.default;
    }
    console.log("com")
    console.log(componentName)

    if(true)return componentName;
    console.log("com")
    console.log(com)
    if (compMap.get(com.name) == null) {
      // const s = "@/views/echarts/waterChart/index.js";
      // const module = defineAsyncComponent(() => import(s));
      // const module = defineAsyncComponent(() => import(`@/views/echarts/waterChart/index.js`));
      // const module = await import(`${com.component}`);
      // const module = await defineAsyncComponent(async () => import(`${com.component}`) );
      // const module = await import(`@/views/about/index.vue`);
      console.log("module")
      console.log(module)

      compMap.set(com.name, module.default)
    }
    // const c = compMap.get(com.name);
    // console.log("c11111111")
    // console.log(c)
    // console.log(compMap)
    return compMap.get(com.name);

    // return defineAsyncComponent(() =>
    //   import(`@${path}`)
    // )
  }
  const loadA = async () => {
    const module = await import(`@/views/about/index.vue`);
    componentName.value = module.default;
  }


  const loadAllComponent = () => {
    componentsInfo.value.push(
      {
        id: '1-1',
        title: '图表-水型图',
        name: 'waterChart',
        // component: markRaw(defineAsyncComponent(() => import('@/views/about/index.vue') )),
        des: '图表-水行图',
        w: 2,
        h: 18
      }
    );
    componentsInfo.value.push(
      {
        id: '1-2',
        title: '图表-柱状图',
        name: 'about',
        // component: markRaw(defineAsyncComponent(() => import('@/views/echarts/lineChart/index.vue') )),
        des: '图表-柱状图',
        w: 4,
        h: 8
      },
    );
    componentsInfo.value.push(
      {
        id: '1-3',
        title: '图表-折线图',
        name: 'lineChart',
        // component: markRaw(defineAsyncComponent(() => import('@/views/echarts/columnChart/index.vue') )),
        des: '图表-折线图',
        w: 4,
        h: 8
      },
    );
    // if(true) return;
    //
    // for(let i =0;i<componentsInfo.length;i++) {
    //   // const module = await import(`@/views/about/index.vue`);
    //   // const module = await import(componentsInfo[i].component);
    //   const uri = componentsInfo[i].component;
    //
    //   const module = defineAsyncComponent(() => import(uri) );
    //   // const module = defineAsyncComponent(() => import(`@/views/about/index.vue` )) ;
    //
    //   // app.component(componentsInfo[i].name, defineAsyncComponent(() =>
    //   //   import(componentsInfo[i].component)
    //   // ));
    //
    //   console.log("=======")
    //   console.log(module)
    //   compMap.value.set(componentsInfo[i].name, module)
    // }
  }

  const dragstart = (e, item) => {
    e.dataTransfer.effectAllowed = 'move'
    currentDragCom = item
    defaultH = item.h
    defaultW = item.w
  }
  const drag = (e, item) => {
    e.preventDefault && e.preventDefault()
    let parentRect = document.querySelector('.grid-box').getBoundingClientRect();
    let mouseInGrid = false;
    if (((mouseXY.x > parentRect.left) && (mouseXY.x < parentRect.right)) && ((mouseXY.y > parentRect.top) && (mouseXY.y < parentRect.bottom))) {
      mouseInGrid = true;
    }
    if (mouseInGrid === true && (layout.value.findIndex(item => item.i === 'drop')) === -1) {
      layout.value.push({
        x: (layout.value.length * 2) % (colNum || 12),
        y: layout.value.length + (colNum || 12),
        w: defaultW,
        h: defaultH,
        i: 'drop',
        comp: null,
      });
    }
    let index = layout.value.findIndex(item => item.i === 'drop');
    if (index !== -1) {
      try {
        proxy.$refs.gridItem[layout.value.length - 1].$refs.item.style.display = "none";
      } catch {
      }
      let el = proxy.$refs.gridItem[index];
      if (el) {
        el.dragging = { "top": mouseXY.y - parentRect.top, "left": mouseXY.x - parentRect.left };
        let new_pos = el && el.calcXY(mouseXY.y - parentRect.top, mouseXY.x - parentRect.left);
        if (mouseInGrid === true) {
          //  function dragEvent(eventName, id, x, y, h, w)
          proxy.$refs.gridLayout.dragEvent('dragstart', 'drop', new_pos.x || 0, new_pos.y || 0, defaultH, defaultW);
          DragPos.i = String(new Date().getTime());
          DragPos.x = layout.value[index].x;
          DragPos.y = layout.value[index].y;
        }
        if (mouseInGrid === false) {
          proxy.$refs.gridLayout.dragEvent('dragend', 'drop', new_pos.x || 0, new_pos.y || 0, defaultH, defaultW);
          layout.value = layout.value.filter(obj => obj.i !== 'drop');
        }
      }
    }
  }
  const dragend = (e) => {
    let parentRect = document.querySelector('.grid-box').getBoundingClientRect();
    let mouseInGrid = false;
    if (((mouseXY.x > parentRect.left) && (mouseXY.x < parentRect.right)) && ((mouseXY.y > parentRect.top) && (mouseXY.y < parentRect.bottom))) {
      mouseInGrid = true;
    }
    if (mouseInGrid === true) {
      proxy.$refs.gridLayout.dragEvent('dragend', 'drop', DragPos.x, DragPos.y, defaultH, defaultW);
      let delIndex = layout.value.findIndex(item => item.i === 'drop')
      layout.value.splice(delIndex, 1)
      // console.log("loadComp");
      // let loadComp = loadComponent(currentDragCom)
      // console.log("loadComp1");
      // console.log(loadComp);
      layout.value.push({
        x: DragPos.x,
        y: DragPos.y,
        w: currentDragCom.w,
        h: currentDragCom.h,
        i: DragPos.i,
        name: currentDragCom.name,
        // component: currentDragCom.component,
        // component: compMap.value.get(currentDragCom.name),
        // loadComp: loadComp
        // loadComp: compMap.get(currentDragCom.name)
      });
      proxy.$refs.gridLayout.dragEvent('dragend', DragPos.i, DragPos.x, DragPos.y, currentDragCom.h, currentDragCom.w);
      try {
        proxy.$refs.gridItem[layout.value.length].$refs.item.style.display = "block";
      } catch {
      }
    }
  }
  // 尺寸变更后，触发resize事件，使图表resize
  const resizedEvent = e => {
    if (document.createEvent) {
      let ev = new Event('resize')
      window.dispatchEvent(ev)
    } else if (document.createEventObject) {
      window.fireEvent('onresize')
    }
  }
  // 删除item
  const delItem = (item) => {
    let delIndex = layout.value.findIndex(el => el.i === item.i)
    layout.value.splice(delIndex, 1)
  }
</script>

<style scoped lang="scss">
  .container {
    position: relative;
    height: 99%;
    width: 100%;
  }
  .grid-box {
    width: calc(100% - 320px);
    height: 100%;
  }
  .components-box {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 300px;
    border: 1px solid rgba(66, 66, 66, 1);
    /*padding: 12px 20px;*/
    .ctrl-box {
      height: 40px;
      padding: 0 12px;
      color: #fff;
      display: flex;
      align-items: center;
      background: #2d2d2c;
      border: 1px solid rgba(66, 66, 66, 1);
      margin-top: 20px;
      user-select: none;
      -webkit-user-select: none;
    }
  }
</style>

<style lang="scss">
  .vue-grid-layout {
    height: 100% !important;
    background: transparent;
    border: 1px solid rgba(66, 66, 66, 1);
    overflow: auto;
    .vue-grid-item {
      background: #2d2d2c;
      border: 1px solid rgba(66, 66, 66, 1);
      border-radius: 2px;
      padding: 12px 20px;
    }
    .close {
      display: inline-block;
      height: 12px;
      width: 12px;
      position: absolute;
      font-size: 12px;
      top: 5px;
      right: 5px;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.6);
      i {
        font-size: 20px;
      }
      &:hover {
        color: #fff;
      }
    }
    .vue-grid-item.vue-resizable.vue-grid-placeholder {
      background: white !important;
    }
  }
</style>
