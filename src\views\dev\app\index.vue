<template>
  <div class="main-box">
    <div class="card filter">
      <h4 class="title sle">应用
        <el-button style="float:right;margin-left:5px" circle :icon="icons.Refresh" @click="getApp" title="刷新"/>
        <el-button style="float:right" circle :icon="icons.Plus" @click="addApp" title="新增应用"/>
      </h4>
      <el-input v-model="filterText" placeholder="输入关键字进行过滤" clearable/>
      <el-scrollbar :style="{ height: `calc(100% - 107px)`  }">
        <template v-for="app in appData">
          <div v-if="showItem(app)"
               :id="app?.id"
               v-on:click="handleAppClick(app)"
               class="appNode"
               :class="classObject(app?.id)">
            <img v-if="app.biglogo" :src="app.biglogo" style="width: 60px;height:60px; display: inline-block;" alt="">
            <div v-else style="width: 60px;height:60px; display: inline-block;background-color:rgba(136, 158, 236, 1);"></div>
            <span class="appInfo">
            <div>{{ app.name }}</div>
            <div>{{ app.showname }}</div>
          </span>
          </div>
        </template>
      </el-scrollbar>
    </div>

    <div class="table-box" style="background-color:#ffffff">
      <el-tabs v-if="currentApp?.id" v-model="appTabName" class="demo-tabs card filter" style="margin-left:5px;height:100vh">
        <el-tab-pane label="基础配资" name="base">
          <el-form
            ref="formRef"
            :model="currentApp"
            status-icon
            :rules="appRules"
            label-suffix=":"
            :hide-required-asterisk="false"
            label-width="140"
            style="width:90%;padding-top:20px"
          >
            <el-row>
              <el-col :span="8">
                <el-form-item label="应用入口" prop="name">
                  /{{currentApp.name}}

                  <el-link style="margin: 0 20px" type="danger" plain size="small" title="删除应用" @click.stop="del(currentApp?.id)">删除</el-link>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="应用类型">
                  <el-text v-if="currentApp.ismobile!=1" type="primary">PC 应用</el-text>
                  <el-text v-else type="success">H5 应用</el-text>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="排序">
                  <el-input-number v-model="currentApp.sort"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="应用标题" prop="showname">
                  <el-input v-model="currentApp.showname"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="短标题" prop="shotname">
                  <el-input v-model="currentApp.shotname"></el-input>
                </el-form-item>
              </el-col>
<!--              <el-col :span="12">-->
<!--                <el-form-item label="菜单类型" prop="styletype">-->
<!--                  <el-radio-group v-model="currentApp.styletype" size="default">-->
<!--                    <el-radio-button v-for="(t) in menutypes" :label="t.label" :value="t.value"/>-->
<!--                  </el-radio-group>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
              <el-col :span="12">
                <el-form-item label="验证码" prop="validcode">
                  <el-select v-model="currentApp.validcode">
                    <el-option v-for="(t) in validcodetypes" :label="t.label" :value="t.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="自定义CSS" prop="cssfile">
                  <el-input v-model="currentApp.cssfile"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="背景图" prop="loginbackground">
                  <UploadImg v-model:image-url="currentApp.loginbackground" width="245px" height="245px">
                    <template #empty>
                      <el-icon>
                        <PictureRounded/>
                      </el-icon>
                      <span>请上传背景图</span>
                    </template>
                  </UploadImg>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="大LOGO" prop="biglogo">
                  <UploadImg v-model:image-url="currentApp.biglogo" width="245px" height="245px">
                    <template #empty>
                      <el-icon>
                        <PictureRounded/>
                      </el-icon>
                      <span>请上传大LOGO</span>
                    </template>
                  </UploadImg>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="小LOGO" prop="smalllogo">
                  <UploadImg v-model:image-url="currentApp.smalllogo" width="245px" height="245px">
                    <template #empty>
                      <el-icon>
                        <PictureRounded/>
                      </el-icon>
                      <span>请上传小LOGO</span>
                    </template>
                  </UploadImg>
                </el-form-item>
              </el-col>
              <el-col :span="18">
              </el-col>
              <el-divider/>
              <el-col :span="24" style="text-align:right">
                <el-button type="success" plain target="_blank" title="打开应用" @click.stop="openAppHandler(currentApp)">打开应用</el-button>
                <el-button type="primary" title="菜单配置" @click.stop="openAppmenuHandler(currentApp)">菜单配置</el-button>
                <el-button type="success" @click="saveAppAction">保存</el-button>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="自定义配置" name="userdefine">
          <el-form
            ref="formRef"
            :model="currentApp"
            status-icon
            :rules="appRules"
            label-suffix=":"
            :hide-required-asterisk="false"
            label-width="140"
            style="width:90%;padding-top:20px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="开启自定义" prop="isdefine">
                  <el-switch
                    v-model="currentApp.isdefine"
                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="currentApp.isdefine==1">
                <el-form-item label="数据源" prop="dsid">
                  <el-select v-model="currentApp.dsid" clearable>
                    <el-option v-for="(t) in dsList" :key="t?.id" :value="t?.id"
                               :label="t.type + '-' + t.name + '  :  ' + t.ip"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-tabs v-if="currentApp.isdefine==1" v-model="defineTabName" class="demo-tabs card filter"
                     style="margin-left:5px;height:100%">
              <el-tab-pane label="自定义用户表" name="user">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="用户表">
                      <el-input v-model="currentApp.usertable"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="ID字段">
                      <el-input v-model="currentApp.idcolumn"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="账号字段">
                      <el-input v-model="currentApp.accountcolumn"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="昵称字段">
                      <el-input v-model="currentApp.showcolumn"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="密码字段">
                      <el-input v-model="currentApp.pwdcolumn"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="角色组字段">
                      <el-input v-model="currentApp.actoridscolumn"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="管理员字段">
                      <el-input v-model="currentApp.admincolumn"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="状态字段">
                      <el-input v-model="currentApp.statuscolumn"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="部门字段">
                      <el-input v-model="currentApp.deptcolumn"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="自增序列字段">
                      <el-input v-model="currentApp.usertableseq"></el-input>
                    </el-form-item>
                  </el-col>

                </el-row>
              </el-tab-pane>
              <el-tab-pane label="自定义部门表" name="dept">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="部门表">
                      <el-input v-model="currentApp.depttable"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="ID字段">
                      <el-input v-model="currentApp.deptid"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="父ID字段">
                      <el-input v-model="currentApp.deptpid"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="部门名称">
                      <el-input v-model="currentApp.deptname"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="部门领导">
                      <el-input v-model="currentApp.deptleader"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="排序字段">
                      <el-input v-model="currentApp.deptorderseq"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="角色组字段">
                      <el-input v-model="currentApp.deptactorids"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="部门序列字段">
                      <el-input v-model="currentApp.depttableseq"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-tab-pane>
            </el-tabs>

            <el-row style="margin-top:30px">
              <el-col :span="18">
              </el-col>
              <el-col :span="6">
                <el-form-item label="">
                  <el-button type="primary" @click="saveAppAction">保存</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="私有化部署" name="upgrade">
          <div style="height: calc(100vh - 214px)">
            <ProTable
              ref="proTable"
              row-key="id"
              :indent="20"
              stripe
              :columns="upgradeColumns"
              :toolButton="false"
              :request-api="getUpgradeList"
              :init-param="initParam"
              :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
            >
              <!-- 表格 header 按钮 -->
              <template #tableHeader>
                <el-button type="primary" :icon="icons.CirclePlus" @click="">发布新升级</el-button>
                <el-button type="success" :icon="icons.Download" @click="">打包静态资源文件</el-button>
              </template>
              <!-- 表格操作 -->
              <template #operation="scope">
                <el-button type="primary" link :icon="icons.View" @click="openTableInfoDlg(scope.row)">查看</el-button>
                <el-button type="primary" link :icon="icons.Delete" @click="openDrawer('编辑', scope.row)">删除</el-button>
              </template>
            </ProTable>
          </div>
        </el-tab-pane>
      </el-tabs>

      <el-form v-else
               ref="formRef"
               :model="currentApp"
               status-icon
               :rules="appRules"
               label-suffix=":"
               :hide-required-asterisk="false"
               label-width="140" class="card"
               style="width:100%;padding-top:20px;height:100%;margin-left: 5px;"
      >
        <el-row style="width:60%">
          <el-col :span="24">
            <el-form-item label="" label-width="0">
              <h2>新增应用</h2>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应用标题" prop="showname">
              <el-input v-model="currentApp.showname"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应用类型">
              <el-radio-group v-model="currentApp.ismobile" size="default">
                <el-radio-button label="PC 应用" :value="0"/>
                <el-radio-button label="H5 应用" :value="1"/>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="18">
          </el-col>
          <el-col :span="6">
            <el-form-item label="">
              <el-button type="primary" @click="saveAppAction">保存</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </div>
  </div>
</template>

<script setup lang="tsx" name="appIndex">
  import {onMounted, reactive, ref, watch} from "vue";
  import {ElMessage, ElMessageBox, ElNotification, FormRules, FormInstance} from "element-plus";
  import * as icons from "@element-plus/icons-vue";
  import {getAppList, saveApp, delApp} from "@/api/dev/app";
  import {getDsList} from "@/api/dev/ds";
  import {getUpgradeList} from "@/api/dev/upgrade";
  import UploadImg from "@/components/Upload/Img.vue";
  import {Ds} from "@/api/interface";
  import ProTable from "@/components/ProTable/index.vue";
  import {save} from "@/api/dev/enum";


  let dsList = [];

  onMounted(async () => {
    getApp();
    const {data} = await getDsList();
    dsList = data;
  });

  const menutypes = [
    {
      label: "左侧菜单",
      value: 2,
    },
    {
      label: "顶部+左侧菜单",
      value: 1,
    },
    {
      label: "浮动菜单",
      value: 0,
    },
  ]

  const validcodetypes = [
    {
      label: "不启用验证码",
      value: 0,
    },
    {
      label: "文字验证码",
      value: 1,
    },
    {
      label: "算数验证码",
      value: 2,
    },
    {
      label: "拖拽图片验证码",
      value: 3,
    },
  ]

  const appData = ref<{ [id: string]: any }[]>([]);


  const filterText = ref("");

  const showItem = (app) => {
    if (filterText.value == "") return true;

    if (app.name != null && app.name.indexOf(filterText.value) >= 0) return true;
    if (app.showname != null && app.showname.indexOf(filterText.value) >= 0) return true;
    return false;
  }

  const getApp = async (id) => {
    const {data} = await getAppList();

    appData.value = data;

    if (!data || data.length == 0) return;

    if (id) {
      for (var i = 0; i < data.length; i++) {
        if (data[i]?.id == id) {
          Object.assign(currentApp.value, data[i]);
        }
      }
    } else {
      Object.assign(currentApp.value, data[0]);
    }
    initParam.appid = currentApp.value?.id;

  };


  const currentApp = ref({});

  const initParam = reactive({appid: null});


  const handleAppClick = (app) => {
    Object.assign(currentApp.value, app);
    initParam.appid = currentApp.value?.id;
  }

  const classObject = (id) => {
    return currentApp.value?.id == id ? "appSelected" : "appNoselected";
  }

  const del = async (id) => {
    ElMessageBox.confirm(`是否删除该应用?`, "温馨提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      draggable: true
    }).then(async () => {
      const res = await delApp(id);
      ElMessage({
        type: "success",
        message: `删除应用成功!`
      });
      getApp()
    }).catch(() => {
    });
  }

  const appTabName = ref("base");
  const defineTabName = ref("user");

  const addApp = () => {
    currentApp.value = {
      showname: '',
      ismobile: 0,
    };
  }


  const appRules = reactive({
    // type: [{ required: true,trigger: 'change', message: "请选择一个数据库类型" }],
    showname: [{required: true, message: "请输入应用标题"}],
  });


  // 表格配置项
  const upgradeColumns = reactive([
    {type: "index", label: "#", width: 80},
    {prop: "appid", label: "所属应用", sortable: true},
    {prop: "edition", label: "版本", width: 100, sortable: true},
    {prop: "opuser", label: "升级人", width: 100, sortable: true},
    {prop: "sqlfile", label: "DUMP文件", sortable: true},
    {prop: "staticfile", label: "静态文件", sortable: true},
    {prop: "remark", label: "备注", sortable: true},
    {prop: "createtime", label: "创建时间", sortable: true},
    {prop: "operation", label: "操作", width: 180}
  ]);


  const formRef = ref<FormInstance>()

  const saveAppAction = async () => {

    formRef.value!.validate(async valid => {
      if (!valid) return;
      try {
        const resp = await saveApp(currentApp.value);

        if (resp.code == 200) {
          ElMessage.success({message: resp.msg});

          getApp(resp.data)

        } else {
          ElMessage.error({message: resp.msg});
        }


      } catch (error) {
        console.log(error);
      }
    });


  }


  const openAppHandler = (app) => {
    open("/" + app.name )
  }

  const openAppmenuHandler = (app) => {
    open("#/dev/appmenu/" + app.id )
  }


</script>
<style scoped lang="scss">
  .appSelected {
    border: 1px solid var(--el-color-primary);
  }

  .appNoselected {
    border: 1px solid rgba(0, 0, 0, 0);
  }

  .appNode {
    height: 80px;
    font-size: 14px;
    margin: 8px 2px;
    overflow: hidden; /* 确保文本超出容器时会被裁剪 */
    white-space: nowrap; /* 确保文本不会换行 */
    text-overflow: ellipsis; /* 在文本末尾显示省略号 */
  }

  .appInfo {
    width: 110px;
    height: 66px;
    display: inline-block;
    margin-left: 5px;
    overflow: hidden; /* 确保文本超出容器时会被裁剪 */
    white-space: nowrap; /* 确保文本不会换行 */
    text-overflow: ellipsis; /* 在文本末尾显示省略号 */
  }
</style>

