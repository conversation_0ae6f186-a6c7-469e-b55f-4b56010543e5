import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 获取关联表
export const getUnionList = (obj) => {
  console.log(obj)
  if(!obj)return ;
  return http.get(PORT1 + `/../dev/union/list?mid=` + obj.id);
};


// 获取被关联表
export const getUnionedList = (obj) => {
  if(!obj)return ;
  return http.get(PORT1 + `/../dev/unioned/list?mid=` + obj.id);
};

// 获取关联表详情
export const getUnionDetail = (id) => {
  return http.get(PORT1 + `/../dev/union/detail?id=` + id);
};

// 保存关联表
export const saveUnion = (params) => {
  return http.post(PORT1 + `/../dev/union/save` ,params);
};

// 删除数据对象
export const del = (id) => {
  return http.get(PORT1 + `/../dev/union/del?id=` + id );
};




