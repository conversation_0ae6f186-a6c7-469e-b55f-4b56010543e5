<template>
  <el-drawer v-model="drawerVisible" title="新建表" size="450px">
    <el-form
      ref="formRef"
      :model="obj"
      status-icon
      :rules="columnRules"
      :hide-required-asterisk="false"
      label-width="120px"
    >
      <el-form-item label="表名 :" prop="tablename" >
        <el-input v-model="obj.tablename"></el-input>
      </el-form-item>
      <el-form-item label="表注释 :" prop="tablecomment" >
        <el-input v-model="obj.tablecomment" ></el-input>
      </el-form-item>
      <el-form-item label=""  >
        <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts" name="CreateTableDrawer">
import {defineEmits, reactive, ref} from "vue";
import {ElMessage, FormInstance, FormRules} from "element-plus";
import { createTable } from "@/api/dev/ds";


const drawerVisible = ref<boolean>(false);

const obj = ref();

let op = ref(null);


const openDrawer = (param) => {
  obj.value = param;
  drawerVisible.value = true;
};

const emit = defineEmits(['save'])

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {

      const { data } = await createTable(obj.value) ;

      ElMessage.success({ message: `操作成功！` });

      drawerVisible.value = false;
      emit('save');

    } catch (error) {
      console.log(error);
    }
  });
}

const formRef = ref();

const columnRules = reactive({
  tablename: [{ required: true, message: "请输入表名" }],
});

defineExpose({
  openDrawer
});
</script>

