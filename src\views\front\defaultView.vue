<template>
  <component
    :is="obj.comment.viewComment"
  ></component>
</template>

<script setup lang="ts" name="build">
import {provide, reactive, ref} from "vue";
import * as icons from '@element-plus/icons-vue'
import  ZDialog  from '@/components/ZDialog/index.vue'
import { saveView, getView }  from "@/api/dev/view"
import {ElMessage} from "element-plus";

const props = defineProps({
  id: Number,
  mainid: Number,
  hashkey: String,
  type : String,
  title: String,
  fit: String,
  width: Number,
  json: String,
})

const obj = reactive({
  comment: {},
  view: {},
  jsonObj: {},
})

Object.assign(obj.view,props) ;
obj.jsonObj = props.json?JSON.parse(props.json):{} ;
obj.comment.name = props.type.substr(0,1).toUpperCase() + props.type.substr(1);
obj.comment.viewComment = obj.comment.name;

provide('obj',obj);

</script>

