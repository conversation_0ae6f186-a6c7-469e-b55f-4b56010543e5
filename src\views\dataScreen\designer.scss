// 数据大屏设计器样式
.dataScreen-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
}

// 顶部工具栏
.designer-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  background: rgb(255 255 255 / 5%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgb(255 255 255 / 10%);
  .toolbar-left,
  .toolbar-right {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .toolbar-center {
    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
      color: #ffffff;
    }
  }
}

// 主要内容区域
.designer-main {
  display: flex;
  flex: 1;
  height: calc(100vh - 60px);
}

// 左侧组件库
.designer-sidebar-left {
  width: 360px;
  overflow-y: auto;
  background: #1e1e1e;
  border-right: 1px solid #333333;
  .sidebar-title {
    padding: 18px 20px;
    font-size: 15px;
    font-weight: 600;
    color: #ffffff;
    background: #252525;
    border-bottom: 1px solid #333333;
  }
  .component-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 20px;
  }
  .component-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100px;
    padding: 20px 15px;
    text-align: center;
    cursor: grab;
    user-select: none;
    background: #2a2a2a;
    border: 1px solid #404040;
    border-radius: 10px;
    transition: all 0.2s ease;
    &:hover {
      background: #333333;
      border-color: #409eff;
      box-shadow: 0 6px 16px rgb(64 158 255 / 40%);
      transform: translateY(-3px);
    }
    &:active {
      cursor: grabbing;
      transform: translateY(0);
    }
    .component-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      margin-bottom: 10px;
      font-size: 36px;
      color: #409eff;
    }
    .component-name {
      display: block;
      font-size: 13px;
      font-weight: 600;
      line-height: 1.4;
      color: #ffffff;
      text-align: center;
    }
  }

  // 折叠面板样式优化
  :deep(.el-collapse) {
    background: transparent;
    border: none;
    .el-collapse-item {
      border-bottom: 1px solid #333333;
      .el-collapse-item__header {
        height: 48px;
        padding: 0 20px;
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        background: #252525;
        border: none;
        &:hover {
          background: #2a2a2a;
        }
        .el-collapse-item__arrow {
          color: #909399;
        }
      }
      .el-collapse-item__wrap {
        background: #1e1e1e;
        border: none;
        .el-collapse-item__content {
          padding: 0;
        }
      }
      &.is-active {
        .el-collapse-item__header {
          background: #2a2a2a;
          border-bottom: 1px solid #333333;
        }
      }
    }
  }
}

// 中间画布区域
.designer-canvas-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  background: #1a1a1a;
  .canvas-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    padding: 0 20px;
    background: #252525;
    border-bottom: 1px solid #333333;
  }
  .sketch-ruler-container {
    position: relative;
    flex: 1;
    background: #1a1a1a;

    // vue3-sketch-ruler 样式定制
    :deep(.sketch-ruler) {
      .ruler {
        font-size: 11px;
        color: #e0e0e0;
        background: #2a2a2a;
        border-color: #404040;
      }
      .corner {
        background: #2a2a2a;
        border-color: #404040;
      }
      .lines {
        .line {
          background: #409eff;
        }
      }
    }
    .canvas-content {
      position: relative;
      width: 100%;
      height: 100%;
      background-image: radial-gradient(circle at 1px 1px, rgb(255 255 255 / 5%) 1px, transparent 0);
      background-size: 20px 20px;
      .vue-grid-layout {
        min-height: 100%;
        background: transparent;
      }
      .vue-grid-item {
        transition: all 0.2s ease;
        &.selected {
          z-index: 999;
          .component-wrapper {
            border: 2px solid #409eff !important;
            box-shadow: 0 0 10px rgb(64 158 255 / 30%);
          }
        }
        &.vue-grid-placeholder {
          background: rgb(64 158 255 / 20%) !important;
          border: 2px dashed #409eff !important;
        }
        &.resizing {
          opacity: 0.8;
        }
        &.dragging {
          z-index: 1000;
          opacity: 0.8;
        }
      }
      .component-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
        cursor: pointer;
        background: rgb(255 255 255 / 5%);
        backdrop-filter: blur(5px);
        border: 1px solid rgb(255 255 255 / 10%);
        border-radius: 4px;
        &:hover {
          border-color: rgb(64 158 255 / 50%);
        }
        .component-header {
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          z-index: 10;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 30px;
          padding: 0 10px;
          background: rgb(64 158 255 / 90%);
          opacity: 0;
          transition: opacity 0.2s ease;
          .component-title {
            font-size: 12px;
            font-weight: 500;
            color: #ffffff;
          }
        }
        &.selected .component-header {
          opacity: 1;
        }
        .component-content {
          width: 100%;
          height: 100%;
          padding: 0;
          &.has-header {
            height: calc(100% - 30px);
            padding-top: 30px;
          }
        }
      }
    }
  }
}

// 右侧属性面板
.designer-sidebar-right {
  width: 320px;
  overflow-y: auto;
  background: #1e1e1e;
  border-left: 1px solid #333333;
  .sidebar-title {
    padding: 16px 20px;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    background: #252525;
    border-bottom: 1px solid #333333;
  }
  .property-panel {
    padding: 20px;
    :deep(.el-tabs) {
      --el-tabs-header-height: 40px;
      .el-tabs__header {
        margin-bottom: 20px;
        background: #2a2a2a;
        border-radius: 6px;
      }
      .el-tabs__nav-wrap {
        background: transparent;
      }
      .el-tabs__item {
        font-size: 13px;
        font-weight: 500;
        color: #909399;
        background: transparent;
        border: none;
        &.is-active {
          color: #409eff;
          background: rgb(64 158 255 / 10%);
        }
        &:hover {
          color: #409eff;
        }
      }
      .el-tabs__content {
        background: transparent;
      }
    }
    .property-section {
      :deep(.el-form-item) {
        margin-bottom: 16px;
        .el-form-item__label {
          font-size: 13px;
          font-weight: 500;
          color: rgb(255 255 255 / 80%);
        }
        .el-input {
          .el-input__wrapper {
            background: #2a2a2a;
            border: 1px solid #404040;
            box-shadow: none;
            &:hover {
              border-color: #409eff;
            }
            &.is-focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
            }
            .el-input__inner {
              color: #e0e0e0;
              background: transparent;
              &::placeholder {
                color: #666666;
              }
            }
          }
        }
        .el-input-number {
          .el-input__wrapper {
            background: #2a2a2a;
            border: 1px solid #404040;
            .el-input__inner {
              color: #e0e0e0;
            }
          }
          .el-input-number__decrease,
          .el-input-number__increase {
            color: #909399;
            background: #333333;
            border-color: #404040;
            &:hover {
              color: #409eff;
              background: #404040;
            }
          }
        }
        .el-select {
          .el-select__wrapper {
            background: #2a2a2a;
            border: 1px solid #404040;
            box-shadow: none;
            &:hover {
              border-color: #409eff;
            }
            &.is-focus {
              border-color: #409eff;
            }
            .el-select__selected-item {
              color: #e0e0e0;
            }
            .el-select__placeholder {
              color: #666666;
            }
          }
        }
        .el-textarea {
          .el-textarea__inner {
            color: #e0e0e0;
            background: #2a2a2a;
            border: 1px solid #404040;
            &:hover {
              border-color: #409eff;
            }
            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
            }
            &::placeholder {
              color: #666666;
            }
          }
        }
        .el-color-picker {
          .el-color-picker__trigger {
            background: #2a2a2a;
            border: 1px solid #404040;
            &:hover {
              border-color: #409eff;
            }
          }
        }
      }
    }
  }
  .no-selection {
    padding: 40px 20px;
    text-align: center;
    :deep(.el-empty) {
      .el-empty__description {
        color: rgb(255 255 255 / 60%);
      }
    }
  }
}

// 预览模式
.preview-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  .dataScreen-content {
    position: absolute;
    top: 50%;
    left: 50%;
    background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
    transform-origin: 0 0;
  }
  .dataScreen-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    padding: 0 40px;
    background: rgb(255 255 255 / 5%);
    border-bottom: 1px solid rgb(255 255 255 / 10%);
    .header-lf,
    .header-ri {
      flex: 1;
    }
    .header-ct {
      flex: 2;
      text-align: center;
      .header-ct-title {
        span {
          font-size: 24px;
          font-weight: 600;
          color: #ffffff;
        }
      }
    }
    .header-screening,
    .header-time {
      font-size: 14px;
      color: #ffffff;
    }
  }
  .dataScreen-main {
    height: calc(100% - 80px);
    padding: 20px;
  }
}

// 响应式适配
@media (width <= 1200px) {
  .designer-sidebar-left {
    width: 300px;
  }
  .designer-sidebar-right {
    width: 280px;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  background: rgb(255 255 255 / 10%);
}
::-webkit-scrollbar-thumb {
  background: rgb(255 255 255 / 30%);
  border-radius: 3px;
  &:hover {
    background: rgb(255 255 255 / 50%);
  }
}
