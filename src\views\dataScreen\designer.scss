// 数据大屏设计器样式
.dataScreen-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// 顶部工具栏
.designer-toolbar {
  height: 60px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  backdrop-filter: blur(10px);

  .toolbar-left,
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .toolbar-center {
    h2 {
      color: #fff;
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .header-time {
    color: #fff;
    font-size: 14px;
    margin-left: 20px;
  }
}

// 主要内容区域
.designer-main {
  flex: 1;
  display: flex;
  height: calc(100vh - 60px);
}

// 左侧组件库
.designer-sidebar-left {
  width: 280px;
  background: rgba(255, 255, 255, 0.05);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow-y: auto;

  .sidebar-title {
    padding: 20px;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .component-list {
    padding: 15px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .component-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px 10px;
    text-align: center;
    cursor: grab;
    transition: all 0.3s ease;
    user-select: none;

    &:hover {
      background: rgba(64, 158, 255, 0.1);
      border-color: #409eff;
      transform: translateY(-2px);
    }

    &:active {
      cursor: grabbing;
    }

    .component-icon {
      font-size: 24px;
      color: #409eff;
      margin-bottom: 8px;
    }

    .component-name {
      display: block;
      color: #fff;
      font-size: 12px;
      line-height: 1.2;
    }
  }
}

// 中间画布区域
.designer-canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.2);

  .canvas-toolbar {
    height: 50px;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
  }

  .designer-canvas {
    flex: 1;
    position: relative;
    overflow: auto;
    background-image: 
      radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.1) 1px, transparent 0);
    background-size: 20px 20px;
    min-height: 600px;
    transform-origin: top left;

    .vue-grid-layout {
      background: transparent;
    }

    .vue-grid-item {
      transition: all 0.2s ease;

      &.selected {
        z-index: 999;
        
        .component-wrapper {
          border: 2px solid #409eff !important;
          box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
        }
      }

      &.vue-grid-placeholder {
        background: rgba(64, 158, 255, 0.2) !important;
        border: 2px dashed #409eff !important;
      }
    }

    .component-wrapper {
      width: 100%;
      height: 100%;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(5px);
      position: relative;
      overflow: hidden;

      .component-header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 30px;
        background: rgba(64, 158, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10px;
        z-index: 10;

        .component-title {
          color: #fff;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }
}

// 右侧属性面板
.designer-sidebar-right {
  width: 320px;
  background: rgba(255, 255, 255, 0.05);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow-y: auto;

  .sidebar-title {
    padding: 20px;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .property-panel {
    padding: 20px;

    .el-tabs {
      --el-tabs-header-height: 40px;
    }

    .property-section {
      .el-form-item {
        margin-bottom: 15px;

        .el-form-item__label {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .no-selection {
    padding: 40px 20px;
    text-align: center;
  }
}

// 预览模式
.preview-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;

  .dataScreen-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform-origin: 0 0;
    background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
  }

  .dataScreen-header {
    height: 80px;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;

    .header-lf,
    .header-ri {
      flex: 1;
    }

    .header-ct {
      flex: 2;
      text-align: center;

      .header-ct-title {
        span {
          color: #fff;
          font-size: 24px;
          font-weight: 600;
        }
      }
    }

    .header-screening,
    .header-time {
      color: #fff;
      font-size: 14px;
    }
  }

  .dataScreen-main {
    height: calc(100% - 80px);
    padding: 20px;
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .designer-sidebar-left {
    width: 240px;
  }

  .designer-sidebar-right {
    width: 280px;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}
