<template>
  <Component :is="comp" v-bind="obj"></Component>
</template>

<script setup lang="ts" name="Preview">
import {onMounted, reactive, ref, watch, shallowRef} from "vue";
import { useRoute } from "vue-router";
import { getView } from "@/api/dev/view";
import { useTabsStore } from "@/stores/modules/tabs";
import router from "@/routers/dev";

const route = useRoute();

const comp = shallowRef();
const obj = reactive({});

onMounted(() => {
  load();
});

const props = defineProps({
  hashkey: String,
  data: Object,
})



console.log("tabStore")
console.log(router)
// const tabStore = useTabsStore();

const load = async () => {

  let hashkey = props.hashkey || route.params.key;

  if(!hashkey) {
    let k = location.hash.indexOf('/',2);
    if(k>0) {
      hashkey = location.hash.substring(k+1);
    }
  }

  if(!hashkey) return;
  const { data } = await getView(hashkey);

  Object.assign(obj,data);

  obj.data = props.data;

  if(obj.title) {
    // tabStore.setTabsTitle( obj.title)
  }

  let c;

  switch (obj.type) {
    case "bi":
      c = await import(`@/views/dashboard/share/index.vue`);
      comp.value = c.default;
      break;
    case "add":
    case "detail":
    case "edit":
    case "query":
      // c = await import(`@/form/views/view.vue`);
      c = await import(`@/views/front/FormView.vue`);
      comp.value = c.default;
      break;
    case "ZGrid":
      c = await import(`./defaultView.vue`);
      // c = await import(`@/form/views/Preview.vue`);
      comp.value = c.default;
      break;
  }
  setTimeout(function(){
    console.log("tabStore123")
    // console.log(tabStore)

    } ,1000
  )

}



</script>
