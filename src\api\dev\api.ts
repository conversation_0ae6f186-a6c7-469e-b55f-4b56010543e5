import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 获取API列表
export const getApiList = () => {
  return http.get(PORT1 + `/../dev/api/getApiList`);
};


// 保存API
export const save = (params) => {
  return http.post(PORT1 + `/../dev/api/save` ,params);
};

// 删除API
export const del = (id) => {
  return http.get(PORT1 + `/../dev/api/del?id=` + id );
};

// API测试
export const test = (devaccount,apikeyword,params) => {
  return http.post(PORT1 + `/../api/` + devaccount + `/` + apikeyword, params  );
};





