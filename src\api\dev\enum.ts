import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 获取枚举列表
export const getEnumList = () => {
  return http.get(PORT1 + `/../dev/enum/getEnumList`);
};


// 保存枚举
export const save = (params) => {
  return http.post(PORT1 + `/../dev/enum/save` ,params);
};

// 删除枚举
export const del = (id) => {
  return http.get(PORT1 + `/../dev/enum/del?id=` + id );
};

// 获取枚举数据
export const data = (id) => {
  return http.get(PORT1 + `/../dev/enum/data?id=` + id );
};





