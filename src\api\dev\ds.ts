import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";



// 获取数据源列表
export const getDsList = () => {
  return http.get<Ds.ResDsListParams[]>(PORT1 + `/../dev/datasource/dsList`, {}, { cancel: false });
};


// 获取数据源表列表
export const getDsTables = (params: Ds.ReqDsTableListParams) => {
  return http.post<ResPage<Ds.ResDsTableListParams>>(PORT1 + `/../dev/datasource/tablelist`, params);
};


export const saveDs = (params: Ds.ResDsListParams) => {
  return http.post(PORT1 + `/../dev/datasource/save`, params);
};

export const delDs = (id: number) => {
  return http.get(PORT1 + `/../dev/datasource/del?id=` + id);
};


// 获取数据源表列表
export const getTableInfo = (params) => {
  return http.post(PORT1 + `/../dev/datasource/getTableInfo`, params);
};

export const saveTable = (params) => {
  return http.post(PORT1 + `/../dev/datasource/saveTable`, params);
};


export const saveColumn = (params) => {
  return http.post(PORT1 + `/../dev/datasource/saveColumn`, params);
};


export const delColumn = (params) => {
  return http.post(PORT1 + `/../dev/datasource/delColumn`, params);
};

export const createTable = (params) => {
  return http.post(PORT1 + `/../dev/datasource/createTable`, params);
};

export const delTable = (params) => {
  return http.post(PORT1 + `/../dev/datasource/delTable`, params);
};


export const makeMIS = (dsid,tablename) => {
  return http.post(PORT1 + `/../dev/datasource/makeMIS`, {dsid:dsid,tablename:tablename} );
};

export const makeBI = (dsid,tablename) => {
  return http.post(PORT1 + `/../dev/datasource/makeBI`, {dsid:dsid,tablename:tablename} );
};

export const uploadMake = (fileDef) => {
  return http.post(PORT1 + `/../dev/datasource/_upload`, fileDef );
};






