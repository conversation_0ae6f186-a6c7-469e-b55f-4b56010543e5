<template>
  <el-dialog v-model="dialogVisible" title="上传文件" size="450px">
    <el-form
      ref="formRef"
      :model="obj"
      status-icon
      :hide-required-asterisk="false"
      label-width="120px"
    >
      <el-form-item label="同名替换 :" >
        <el-radio-group v-model="obj.replace">
          <el-radio-button label="替换" :value="true"></el-radio-button>
          <el-radio-button label="不替换" :value="false"></el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="文件类型 :" >
        <el-upload
          class="upload-demo"
          :action="uploadUrl"
          :auto-upload="true"
          accept=".htm,.html,.js,.css,.xml,.txt"
          :data="{replace:obj.replace,path:obj.path}"
          :headers="headObj"
          :with-credentials="true"
          :show-file-list="false"
          :on-success="uploadSuccess"
        >
          <el-button type="primary">上传文本类文件</el-button>
        </el-upload>
        <el-upload style="padding-left: 30px"
          class="upload-demo"
          :action="uploadUrl"
          :auto-upload="true"
          accept="image/*,audio/*,video/*"
          :data="{replace:obj.replace,path:obj.path}"
          :headers="headObj"
          :with-credentials="true"
          :show-file-list="false"
          :on-success="uploadSuccess"
        >
          <el-button type="success">上传图片/视频类文件</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts" name="UploadDialog">
import {defineEmits, onMounted, reactive, ref} from "vue";
import {ElMessage, FormInstance, FormRules} from "element-plus";
import {useUserStore} from "@/stores/modules/user";


const dialogVisible = ref<boolean>(false);
const headObj = ref();
const uploadUrl = import.meta.env.VITE_API_URL + "/dev/file/upload"

const obj = ref({
  replace: true,
  path: '/',
});

onMounted(() => {
  const userStore = useUserStore();
  headObj.value = new Headers({
    _zreport_dev_token : userStore._zreport_dev_token
  });

})


const openDialog = (param) => {
  Object.assign(obj.value,param);
  console.log(param)
  console.log(obj.value)
  dialogVisible.value = true;
};

const emit = defineEmits(['uploaded'])

const formRef = ref();

const uploadSuccess = () => {
  ElMessage.success("上传成功");
  dialogVisible.value = false;
  emit('uploaded');
}

defineExpose({
  openDialog: openDialog
});
</script>

