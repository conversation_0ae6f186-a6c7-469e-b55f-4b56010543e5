// ZData 数据可视化构建器样式
.zdata-builder {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// 顶部工具栏
.zdata-toolbar {
  height: 60px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  backdrop-filter: blur(10px);

  .toolbar-left,
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .toolbar-center {
    h2 {
      color: #fff;
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
  }
}

// 主要内容区域
.zdata-main {
  flex: 1;
  display: flex;
  height: calc(100vh - 60px);
}

// 左侧组件库
.zdata-sidebar-left {
  width: 320px;
  background: #1e1e1e;
  border-right: 1px solid #333;
  overflow-y: auto;

  .sidebar-title {
    padding: 16px 20px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid #333;
    background: #252525;
  }

  .component-list {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .component-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80px;
    padding: 16px 12px;
    text-align: center;
    cursor: grab;
    user-select: none;
    background: #2a2a2a;
    border: 1px solid #404040;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background: #333;
      border-color: #409eff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }

    &:active {
      cursor: grabbing;
      transform: translateY(0);
    }

    .component-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      margin-bottom: 8px;
      font-size: 28px;
      color: #409eff;
    }

    .component-name {
      display: block;
      font-size: 12px;
      font-weight: 500;
      line-height: 1.3;
      color: #e0e0e0;
      text-align: center;
    }
  }

  // 折叠面板样式
  :deep(.el-collapse) {
    border: none;
    background: transparent;

    .el-collapse-item {
      border-bottom: 1px solid #333;

      .el-collapse-item__header {
        background: #252525;
        color: #fff;
        border: none;
        padding: 0 20px;
        height: 48px;
        font-size: 14px;
        font-weight: 600;

        &:hover {
          background: #2a2a2a;
        }

        .el-collapse-item__arrow {
          color: #909399;
        }
      }

      .el-collapse-item__wrap {
        border: none;
        background: #1e1e1e;

        .el-collapse-item__content {
          padding: 0;
        }
      }

      &.is-active {
        .el-collapse-item__header {
          background: #2a2a2a;
          border-bottom: 1px solid #333;
        }
      }
    }
  }
}

// 中间画布区域
.zdata-canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;

  .canvas-toolbar {
    height: 50px;
    background: #252525;
    border-bottom: 1px solid #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
  }

  .canvas-wrapper {
    flex: 1;
    position: relative;
    background: #1a1a1a;

    // vue3-sketch-ruler 样式定制
    :deep(.sketch-ruler) {
      .ruler {
        background: #2a2a2a;
        border-color: #404040;
        color: #e0e0e0;
        font-size: 11px;
      }

      .corner {
        background: #2a2a2a;
        border-color: #404040;
      }

      .lines {
        .line {
          background: #409eff;
        }
      }
    }

    .canvas-content {
      position: relative;
      background-image: radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.05) 1px, transparent 0);
      background-size: 20px 20px;
      overflow: hidden;
    }
  }
}

// 右侧属性面板
.zdata-sidebar-right {
  width: 320px;
  background: #1e1e1e;
  border-left: 1px solid #333;
  overflow-y: auto;

  .sidebar-title {
    padding: 16px 20px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid #333;
    background: #252525;
  }

  .property-panel {
    padding: 20px;

    :deep(.el-tabs) {
      --el-tabs-header-height: 40px;
      
      .el-tabs__header {
        background: #2a2a2a;
        border-radius: 6px;
        margin-bottom: 20px;
      }

      .el-tabs__nav-wrap {
        background: transparent;
      }

      .el-tabs__item {
        color: #909399;
        background: transparent;
        border: none;
        font-size: 13px;
        font-weight: 500;

        &.is-active {
          color: #409eff;
          background: rgba(64, 158, 255, 0.1);
        }

        &:hover {
          color: #409eff;
        }
      }

      .el-tabs__content {
        background: transparent;
      }
    }
  }

  .no-selection {
    padding: 40px 20px;
    text-align: center;

    :deep(.el-empty) {
      .el-empty__description {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .zdata-sidebar-left {
    width: 280px;
  }

  .zdata-sidebar-right {
    width: 280px;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

// 通用表单样式
.zdata-form {
  :deep(.el-form-item) {
    margin-bottom: 16px;

    .el-form-item__label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 13px;
      font-weight: 500;
    }

    .el-input {
      .el-input__wrapper {
        background: #2a2a2a;
        border: 1px solid #404040;
        box-shadow: none;

        &:hover {
          border-color: #409eff;
        }

        &.is-focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .el-input__inner {
          color: #e0e0e0;
          background: transparent;

          &::placeholder {
            color: #666;
          }
        }
      }
    }

    .el-input-number {
      .el-input__wrapper {
        background: #2a2a2a;
        border: 1px solid #404040;

        .el-input__inner {
          color: #e0e0e0;
        }
      }

      .el-input-number__decrease,
      .el-input-number__increase {
        background: #333;
        border-color: #404040;
        color: #909399;

        &:hover {
          color: #409eff;
          background: #404040;
        }
      }
    }

    .el-select {
      .el-select__wrapper {
        background: #2a2a2a;
        border: 1px solid #404040;
        box-shadow: none;

        &:hover {
          border-color: #409eff;
        }

        &.is-focus {
          border-color: #409eff;
        }

        .el-select__selected-item {
          color: #e0e0e0;
        }

        .el-select__placeholder {
          color: #666;
        }
      }
    }

    .el-textarea {
      .el-textarea__inner {
        background: #2a2a2a;
        border: 1px solid #404040;
        color: #e0e0e0;

        &:hover {
          border-color: #409eff;
        }

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        &::placeholder {
          color: #666;
        }
      }
    }

    .el-switch {
      --el-switch-on-color: #409eff;
      --el-switch-off-color: #606266;
    }

    .el-color-picker {
      .el-color-picker__trigger {
        background: #2a2a2a;
        border: 1px solid #404040;

        &:hover {
          border-color: #409eff;
        }
      }
    }
  }
}
