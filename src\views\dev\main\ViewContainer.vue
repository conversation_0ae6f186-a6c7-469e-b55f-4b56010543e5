<template>
  <div ref="divDom" class="main-box" :style="{'height': 'calc(100vh - 230px)','background-color': '#ffffff'}">
    <el-scrollbar class="content list" style="height: 100%">
      <div class="content__box">
        <div class="content__item" v-if="curMain.id" @click="add">
          <div class="content__info" >
            <div class="content__menu" >
              <el-tooltip content="新增视图">
                <el-icon style="color:#ffffff;font-size:48px"><Plus /></el-icon>
              </el-tooltip>
            </div>
          </div>
          <div class="content__main">
            <span class="content__name"></span>
            <div class="content__menulist">
              <div class="content__view">
              </div>
              <span class="content__status"></span>
            </div>
          </div>
        </div>

        <div class="content__item"
             v-for="(item,index) in list"
             :key="index"
             @mouseover="item._menu=true"
             @mouseout="item._menu=false">
          <div class="content__info" style="background: var(--el-color-primary);">
            <div style="width:100%;height:100%;">
              <div style="width:100%;height:40%;text-align: center;margin-top:20%; font-size:24px;color:#ffffff">{{item.title?item.title:'(无标题)'}}</div>
              <div style="width:100%;height:20%;text-align: center;font-size:12px;color:#ffffff">{{item.hashkey}}</div>
            </div>
            <div class="content__menu"
                 v-show="item._menu">
              <div class="content__right">
                <el-tooltip content="预览">
                  <el-icon @click="handleView(item,index)"><View /></el-icon>
                </el-tooltip>
              </div>
              <div class="content__btn"
                   @click="handleEdit(item)">
                编辑
              </div>
              <div class="content__list">
                <el-tooltip content="复制">
                  <el-icon @click="handleCopy(item,index)"><DocumentCopy /></el-icon>
                </el-tooltip>
                <el-tooltip content="删除">
                  <el-icon type="danger" @click="handleDel(item)"><Delete /></el-icon>
                </el-tooltip>
              </div>
            </div>
          </div>
          <div class="content__main">
            <span class="content__name">{{item.type}}</span>
            <div class="content__menulist">
              <div class="content__view">
              </div>
              <span class="content__status"
                    :class="{'content__status--active':item.status}">
                  {{item.fit}}
                </span>
            </div>

          </div>
        </div>
      </div>
    </el-scrollbar>
    <CreateMisDrawer ref="createMisRef" @save="refresh"></CreateMisDrawer>
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive,inject } from 'vue'
import {ElMessage, ElMessageBox, ElNotification} from "element-plus";
import { getViewList, addView, delView, cloneView } from '@/api/dev/view';
import  CreateMisDrawer  from "./CreateMisDrawer.vue"

const list = ref([]);

const curMain = inject('curMain')

console.log("curMain")
console.log(curMain)

const createMisRef = ref();

const refresh = async () => {
  const { data } = await getViewList(curMain?.id);
  list.value = data;
}


const add = async () => {
  if(curMain?.type==='1' || curMain?.type===1) {
    ElMessageBox.prompt('请输入您要创建的视图名称', '温馨提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /[^\s]/,
      inputErrorMessage: '请输入视图名称',
      inputValue: `${curMain?.showname}统计`,
    }).then(async ({value}) => {
      const resp = await addView({mainid: curMain.id, title: value, type: 'bi'});

      if (resp.code === 200) {
        ElMessage.success({message: resp.msg});

        refresh();
      } else {
        ElMessage.error({message: resp.msg});
      }
    })
  }else {
    createMisRef.value?.openDrawer({
      mainid: curMain?.id,
      mainname: curMain?.showname,
      type : "ZGrid",
      title: (curMain.showname||'') + "管理"});
  }
}


const handleEdit = (item) => {
  open(`#/dev/build/${item.hashkey}`);
}

const handleView = (item) => {
  open(`#/view/${item.hashkey}`);
}

const handleDel = (item) => {

  ElMessageBox.confirm(`是否删除该视图?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const resp = await delView(item.hashkey);

    if (resp.code == 200) {
      ElMessage.success({message: resp.msg});

      refresh();
    } else {
      ElMessage.error({message: resp.msg});
    }
  })
}

const handleCopy = async (item,index) => {

  ElMessageBox.confirm(`是否复制当前视图?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const resp = await cloneView(item.id);
    if(resp.code === 200 ){
      ElMessage.success(resp.msg);
      refresh();
    }else {
      ElMessage.error(resp.msg);
    }
  })

}


defineExpose({
  refresh
});

</script>

<style lang="scss">
  @import "@/styles/list.scss";
</style>
