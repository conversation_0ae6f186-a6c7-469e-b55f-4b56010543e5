import {Router} from "vue-router";

export const jump = (router:Router, menuItem: Menu.MenuOptions | Record<string, any>) => {
  if (menuItem.meta.isLink) {
    window.open(menuItem.meta.isLink, "_blank");
  } else {
    // const to = {
    //   name: menuItem.name,
    //   path: menuItem.path,
    //   params: menuItem.meta.params,
    //   query: menuItem.meta.query
    // }
    console.log("jump")
    console.log(menuItem)
    // console.log(to)
    router.push(menuItem.path);
  }

};
