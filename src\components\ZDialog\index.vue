<template>
  <el-dialog
    v-model="dlgRef.dialogVisible"
    :title="dlgRef.title"
    :destroy-on-close="true"
    :width="dlgRef.width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    draggable
  >
    <component
      :is="dlgRef.comName"
      v-bind="dlgRef.comProp"
      v-model="dlgRef.comValue"
    ></component>
  </el-dialog>
</template>
<script setup lang="tsx" name="ZDialog">
import {onMounted, reactive, ref, watch} from "vue";

let defaultRef = {
  dialogVisible: false,
  title: "标题",
  width: "50%",
  comName: "about",
  comValue: null,
  comProp: {},
}

let dlgRef = ref({});


const openDlg = (params) => {
  Object.assign(dlgRef.value, defaultRef);
  Object.assign(dlgRef.value, params);
  dlgRef.value.dialogVisible = true;
}

defineExpose({
  openDlg
})

</script>
