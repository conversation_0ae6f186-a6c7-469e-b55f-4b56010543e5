<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${op}数据源`"
    :destroy-on-close="true"
    width="900"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    draggable
  >
    <el-form
      ref="formRef"
      :model="dsForm"
      :rules="rules"
      status-icon
      :hide-required-asterisk="false"
       label-width="120px"
    >
      <el-form-item label="数据库类型 :" style="margin-top: 20px" >
        <el-radio-group v-model="dsForm.type" size="small">
          <el-radio-button v-for="(t) in dsTypeList" :label="t.label" :value="t.value" :disabled="dsForm.id && dsForm.type != t.value" />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="名称 :" prop="name">
        <el-input v-model="dsForm.name" clearable />
        <div class="el-upload__tip"> 仅允许输入英文字母和数字</div>
<!--        <template #tip>-->
<!--          <slot name="tip">-->
<!--          </slot>-->
<!--        </template>-->
      </el-form-item>
      <el-form-item label="服务器地址 :" clearable prop="ip">
        <el-input v-model="dsForm.ip" clearable></el-input>
      </el-form-item>
      <el-form-item label="端口 :" prop="port">
        <el-input v-model="dsForm.port" clearable></el-input>
      </el-form-item>
      <el-form-item label="数据库 / sid :" prop="dbname">
        <el-input v-model="dsForm.dbname" clearable></el-input>
      </el-form-item>
      <el-form-item label="用户名 :" prop="username">
        <el-input v-model="dsForm.username" clearable></el-input>
      </el-form-item>
      <el-form-item label="密码 :" prop="password">
        <el-input v-model="dsForm.password" clearable show-password></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button style="float: left" v-if="dsForm.id" @click="cloneObj">克隆</el-button>
        <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="EditDsDialog">
import {defineEmits, reactive, ref} from "vue";
import {ElNotification, UploadRequestOptions, UploadRawFile, FormRules, FormInstance, ElMessage} from "element-plus";
import {Ds} from "@/api/interface";
import {getDsList, saveDs} from "@/api/dev/ds";


let dsForm = reactive({})

const dsTypeList = [
  {label: "Mysql",value:"mysql"},
  {label: "Mariadb",value:"mariadb"},
  {label: "Oracle",value:"oracle"},
  {label: "SQLServer2008",value:"sqlserver2008"},
  {label: "SQLServer",value:"sqlserver"},
  {label: "优炫",value:"uxdb"},
  {label: "达梦",value:"dm"},
  {label: "DB2",value:"db2"},
  {label: "PostgreSQL",value:"postgresql"},
  {label: "ClickHouse",value:"clickhouse"},
]

const op = ref<string>("新增");


const formRef = ref<FormInstance>()

const rules = reactive<FormRules<Ds.ResDsListParams>>({
  // type: [{ required: true,trigger: 'change', message: "请选择一个数据库类型" }],
  name: [{ required: true, message: "请输入数据源名称" }],
  ip: [{ required: true, message: "请输入服务器地址" }],
  port: [{ required: true, message: "请输入端口号" }],
  dbname: [{ required: true, message: "请输入数据库 / sid" }],
  username: [{ required: true, message: "请输入数据库账号" }],
  password: [{ required: true, message: "请输入数据库密码" }]
});

// dialog状态
const dialogVisible = ref(false);


const isModi = ref(false);

const api = reactive({f:Function});

// 接收父组件参数
const acceptParams = (params) => {

  dsForm = reactive({})
  if(params)Object.assign(dsForm, params);

  isModi.value = dsForm && dsForm.id  && dsForm.id>0
  op.value = isModi.value? "修改":"新增";
  if(!dsForm.type) dsForm.type = "mysql";

  dialogVisible.value = true;
};


const emit = defineEmits(['save'])

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const resp = await saveDs(dsForm) ;

      if(resp.code==200) {
        ElMessage.success({ message: resp.msg });

        dialogVisible.value = false;

        emit('save');

      }else {
        ElMessage.error({ message: resp.msg });
      }


    } catch (error) {
      console.log(error);
    }
  });


}

const cloneObj = () => {
  dsForm.id = 0;
  op.value = isModi.value? "修改":"新增";
}

defineExpose({
  acceptParams
});
</script>
