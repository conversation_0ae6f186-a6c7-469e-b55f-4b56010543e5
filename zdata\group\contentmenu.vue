<template>
  <div class="contentmenu"
       id="avue-data-menu">
    <div class="contentmenu__item"
         @click="handleLock()"> <i class="el-icon-lock"></i>
      {{contain.activeObj.lock?'解锁':'锁定'}}
    </div>
    <div class="contentmenu__item"
         @click="contain.isFolder?handleLogout():handleCompose()"> <i class="el-icon-document-copy"></i>
      {{contain.isFolder?'解散':'组合'}}
    </div>
    <div class="contentmenu__item"
         @click="handleDel()"> <i class="el-icon-delete"></i>
      删除
    </div>
    <div class="contentmenu__item"
         @click="handleCopy()"><i class="el-icon-money"></i>
      复制
    </div>
    <div class="contentmenu__item"
         @click="handleTop()"><i class="el-icon-arrow-up"></i>置顶
    </div>
    <div class="contentmenu__item"
         @click="handleBottom()"><i class="el-icon-arrow-down"></i>置底
    </div>
    <div class="contentmenu__item"
         @click="handleStepTop()"><i class="el-icon-arrow-up"></i>上移
    </div>
    <div class="contentmenu__item"
         @click="handleStepBottom()"><i class="el-icon-arrow-down"></i>下移
    </div>
    <div class="contentmenu__item">
      <small style="font-size:10px">Tip:按住Ctrl可以选择多个图层</small>
    </div>
    <div class="contentmenu__item">
      <small style="font-size:10px">Tip:按空格可以拖拽画布</small>
    </div>
  </div>
</template>


<script setup lang="ts">
import { inject ref } from "vue";
import { createFile, uuid } from '../utils';
import { ElMessageBox, ElMessage } from "element-plus";

const contain = inject('contain');

const handleStepBottom  = () => {
  handleCommon(false, true);
}

const handleStepTop = () => {
  handleCommon(true, true);
}
//文件夹成组逻辑
const handleCompose = () => {
  ElMessageBox.confirm(`是否组合所选择的图层?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    let folder = createFile()
    contain.active.forEach(ele => {
      let { itemList, itemIndex } = this.contain.findnav(ele);
      let obj = itemList.splice(itemIndex, 1)[0];
      folder.children.push(obj);
    });
    contain.nav.unshift(folder);
    contain.handleInitActive();
  }).catch(() => { })
}

//文件夹解散逻辑
const handleLogout = () => {
  let ele = contain.activeObj
    ElMessageBox.confirm(`是否解散${ele.name}图层?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    let { itemList, itemIndex } = this.contain.findnav(ele.index);
    const list = deepClone(ele.children)
    itemList.splice(itemIndex, 1);
    list.forEach(item => itemList.push(item));
    contain.handleInitActive();
  }).catch(() => { })
}

//删除组件的方法
const handleDel = () => {
    ElMessageBox.confirm(`是否删除所选图层?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    contain.active.forEach(ele => {
      const { itemList, itemIndex } = this.contain.findnav(ele);
      itemList.splice(itemIndex, 1);
    });
    contain.handleInitActive();
  }).catch(() => { })
}


//复制组件的方法
const handleCopy = () => {
  //寻找父类
  contain.active.forEach(ele => {
    const { item, itemList } = this.contain.findnav(ele);
    let obj = this.deepClone(item);
    let index = uuid();
    obj.name = obj.name + '（复制）'
    obj.index = index;
    if (obj.children) {
      obj.menu = false
      obj.children.forEach(ele => ele.index = uuid())
    }
    itemList.unshift(obj)
  });
  contain.handleInitActive();
}


// 图层的上下移动方法
const handleCommon = (top = false, step = false) => {
  contain.active.forEach(ele => {
    let { itemList, itemIndex } = contain.findnav(ele);
    let obj = itemList.splice(itemIndex, 1)[0];
    if (step) {
      itemList.splice(top ? (itemIndex - 1) : (itemIndex + 1), 0, obj)
    } else {
      itemList[top ? 'unshift' : 'push'](obj)
    }
  })
}

const handleTop = () => {
  handleCommon(true);
}

const handleBottom = () => {
  handleCommon();
}

const handleLock = () => {
  contain.active.forEach(ele => {
    let { item } = this.contain.findnav(ele)
    item.lock = !item.lock;
    if (item.children) {
      item.children.forEach(e => {
        e.lock = item.lock
      })
    }
  })
  contain.active = []
}
</script>

<style scoped lang="scss">
.contentmenu {
  width: 200px;
  display: none;
  z-index: 99999;
  list-style: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 0;
  background: #27343e;
  color: #bcc9d4;
}
.contentmenu__item {
  z-index: 10000;
  list-style: none;
  padding: 8px 12px;
  cursor: pointer;
  position: relative;
  font-size: 12px;
}
.contentmenu__item:hover {
  background-color: rgba(0, 192, 222, 0.1);
}
.contentmenu__item i {
  margin-right: 5px;
}
.contentmenu__item :first-child {
  padding-top: 5px;
}
</style>
