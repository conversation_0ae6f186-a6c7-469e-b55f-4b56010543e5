import { createApp } from "vue";
import App from "./App.vue";
// reset style sheet
import "@/styles/reset.scss";
// CSS common style sheet
import "@/styles/common.scss";
// iconfont css
import "@/assets/iconfont/iconfont.scss";
// font css
import "@/assets/fonts/font.scss";
// element css
import "element-plus/dist/index.css";

// element dark css
import "element-plus/theme-chalk/dark/css-vars.css";
// custom element dark css
import "@/styles/element-dark.scss";
// custom element css
import "@/styles/element.scss";
// svg icons
// import "virtual:svg-icons-register";
// element plus
import ElementPlus from "element-plus";

// import formCreate from '@form-create/element-ui'; // 引入 FormCreate
// import FcDesigner from '@form-create/designer'
// import FcDesigner from '@/fcForm/index';

// element icons
import * as Icons from "@element-plus/icons-vue";
// custom directives
// import directives from "@/directives/index";
// vue Router
import router from "@/routers/dev/index";
// vue i18n
import I18n from "@/languages";
// pinia store
import pinia from "@/stores";


// import GUI from "@/components/UI";
// errorHandler
import errorHandler from "@/utils/errorHandler";
// vue-grid-layout 拖拽布局
import VueGridLayout from "vue-grid-layout"; // 引入layout


const app = createApp(App);

app.config.globalProperties.$icons = [];

app.config.errorHandler = errorHandler;

// register the element Icons component
Object.keys(Icons).forEach(key => {
  app.config.globalProperties.$icons.push(key);
  app.component(key, Icons[key as keyof typeof Icons]);
});



// import ZGrid from '@/components/ZGrid/index.vue';
// import ZGridOption from '@/components/ZGrid/option.vue';
// app.component("ZGrid",ZGrid);
// app.component("ZGridOption",ZGridOption);

// import {VFormDesigner} from '@/../lib/vform/designer.umd.js';  //引入VForm3库
// import '@/../lib/vform/designer.style.css'  //引入VForm3样式

// app.use(VFormDesigner);

// import VForm3 from '@/../lib/vform1/designer.umd.js'
// app.use(VForm3)
// app.use(GUI);

app.use(ElementPlus);
// .use(directives)
app.use(router).use(I18n).use(pinia).use(VueGridLayout);


app.mount("#app");
