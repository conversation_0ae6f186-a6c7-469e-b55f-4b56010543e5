<!-- 柱状图配置 -->
<template>
  <el-card class="box-card" style="margin: 0 -20px 0 -20px">
    <div slot="header" class="clearfix">
      <span>左侧</span>
      <el-switch v-model="obj.jsonObj.leftOption.showLeft" style="float: right; padding: 3px 0"></el-switch>
    </div>
    <template v-if="obj.jsonObj.leftOption?obj.jsonObj.leftOption.showLeft:false">
      <el-form-item label="显示标题">
        <el-switch v-model="obj.jsonObj.leftOption.showTitle"></el-switch>
      </el-form-item>
      <el-form-item label="显示搜索">
        <el-switch v-model="obj.jsonObj.leftOption.showSearch"></el-switch>
      </el-form-item>
      <el-form-item label="左侧宽度" >
        <el-input v-model="obj.jsonObj.leftOption.leftWidth"  placeholder="请输入左侧宽度" >
          <template #append>px</template>
        </el-input>
      </el-form-item>
      <el-form-item label="左侧API" >
        <el-input type="textarea" v-model="obj.jsonObj.leftOption.leftApi"  placeholder="请输入左侧API" ></el-input>
      </el-form-item>
      <el-form-item label="左侧传参" >
        <el-input v-model="obj.jsonObj.leftOption.paramName"  placeholder="请输入左侧传参" ></el-input>
      </el-form-item>
    </template>

  </el-card>

  <el-card class="box-card" style="margin: 0 -20px 0 -20px">
    <div slot="header" class="clearfix" style="line-height:30px">
      <span>表格</span>
    </div>

    <el-form-item label="标题" >
      <el-input v-model="obj.view.title"></el-input>
    </el-form-item>
    <el-form-item label="显示标题">
      <el-switch v-model="obj.jsonObj.tableOption.showTitle"></el-switch>
    </el-form-item>
    <template v-if="obj.jsonObj.tableOption.showTitle">
      <el-form-item label="图标">
        <SelectIcon v-model="obj.jsonObj.tableOption.icon" clearable ></SelectIcon>
      </el-form-item>
      <el-form-item label="标题颜色" prop="titleColor">
        <el-color-picker v-model="obj.jsonObj.tableOption.titleColor" show-alpha />
      </el-form-item>
    </template>

    <el-form-item label="查询面板" >
      <el-select v-model="obj.jsonObj.tableOption.queryView" placeholder="请选择" clearable>
        <el-option
          v-for="item in subViewList('query')"
          :key="item.id"
          :label="item.title"
          :value="item.hashkey">
        </el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="是否分页">
      <el-switch v-model="obj.jsonObj.tableOption.pagination"></el-switch>
    </el-form-item>
    <template v-if="!!obj.jsonObj.tableOption.pagination">
      <el-form-item label="分页" >
        <draggable v-model="obj.jsonObj.tableOption.pageable.pageSizes" itemKey="" >
          <template #item="{element,index}">
            <div>
              <el-button type="success" link :icon="icons.Pointer"></el-button>
              <el-input-number v-model="obj.jsonObj.tableOption.pageable.pageSizes[index]" style="width:80%" ></el-input-number>
              <el-button type="primary" link :icon="icons.Delete" style="font-size: 12px" @click="obj.jsonObj.tableOption.pageable.pageSizes.splice(index,1)"> </el-button>
            </div>
          </template>
        </draggable>
        <el-button type="success" :icon="icons.Plus" text title="增加分页项" style="font-size: 12px" @click="obj.jsonObj.tableOption.pageable.pageSizes.push(obj.jsonObj.tableOption.pageable.pageSizes.slice(-1)[0]+10)"> </el-button>
      </el-form-item>
      <el-form-item label="每页条数" >
        <el-select v-model="obj.jsonObj.tableOption.pageable.pageSize" placeholder="请选择">
          <el-option
            v-for="item in obj.jsonObj.tableOption.pageable.pageSizes"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
    </template>

    <el-form-item label="查询条件">
      <el-input type="textarea" :rows="1" v-model="obj.jsonObj.tableOption.defaultwhere"></el-input>
    </el-form-item>
    <el-form-item label="排序">
      <el-input type="textarea" :rows="1" v-model="obj.jsonObj.tableOption.orderby"></el-input>
    </el-form-item>
    <el-form-item label="横向斑马纹">
      <el-switch v-model="obj.jsonObj.tableOption.stripe"></el-switch>
    </el-form-item>
    <el-form-item label="纵向边框">
      <el-switch v-model="obj.jsonObj.tableOption.border"></el-switch>
    </el-form-item>
    <el-form-item label="显示合计">
      <el-switch v-model="obj.jsonObj.tableOption.showSummary"></el-switch>
    </el-form-item>
    <el-form-item label="索引列">
      <el-radio-group v-model="obj.jsonObj.tableOption.pkType">
        <el-radio-button label="单选" value="radio" />
        <el-radio-button label="多选" value="selection" />
        <el-radio-button label="序号" value="index" />
      </el-radio-group>
    </el-form-item>
    <el-divider></el-divider>
    <p>字段配置<el-checkbox style="float:right" @change="selectAll('columns')" label="全选"></el-checkbox></p>
    <el-form-item label-width="0" >
      <draggable v-model="obj.jsonObj.tableOption.columns" itemKey="name"  style="width: 100%">
        <template #item="{element,index}" style="width: 100%">
          <el-row :key="index" :class="getRowClass(index)">
            <el-col :span="10">
              <label class="el-col-24" >
                <el-checkbox v-model="element.selected" ></el-checkbox>
                {{element.label}}
              </label>
            </el-col>
            <el-col :span="12" style="display: inline-block">{{element.prop}}</el-col>
            <el-col :span="2" >
              <el-link type="primary" link :icon="icons.Edit" @click="editColumn(element)" title="编辑"></el-link>
            </el-col>
          </el-row>
        </template>
      </draggable>
    </el-form-item>

    <el-divider></el-divider>
    <p>头部按钮<el-checkbox style="float:right" @change="selectAll('topBtns')" :value="false" label="全选"></el-checkbox></p>
    <el-form-item label-width="0" >
      <draggable v-model="obj.jsonObj.tableOption.topBtns" itemKey="text"  style="width: 100%">
        <template #item="{element,index}" style="width: 100%">
          <el-row :key="index" :class="getRowClass(index)">
            <el-col :span="10">
              <label class="el-col-24" >
                <el-checkbox v-model="element.selected" ></el-checkbox>
                {{element.label}}
              </label>
            </el-col>
            <el-col :span="10" style="display: inline-block">{{element.type}}</el-col>
            <el-col :span="4" >
              <el-link :style="{ visibility: element.isDefault ?'hidden' : 'visible' }" type="danger" link :icon="icons.Delete" title="删除" @click="obj.jsonObj.tableOption.topBtns.splice(index,1)"></el-link>
              <el-link type="primary" link :icon="icons.Edit" @click="element.isTop=true;editBtn(element)" title="编辑" style="margin-left:10px"></el-link>
            </el-col>
          </el-row>
        </template>
      </draggable>
      <el-button type="primary" :icon="icons.Plus" style="font-size: 12px" @click="obj.jsonObj.tableOption.topBtns.push({label:'头部按钮',type:'primary'})"> 增加头部按钮</el-button>
    </el-form-item>
    <el-divider></el-divider>
    <p>行按钮<el-checkbox style="float:right" @change="selectAll('rowBtns')" :value="false" label="全选"></el-checkbox></p>
    <el-form-item label-width="0" >
      <draggable v-model="obj.jsonObj.tableOption.rowBtns" itemKey="text"  style="width: 100%">
        <template #item="{element,index}" style="width: 100%">
          <el-row :key="index" :class="getRowClass(index)">
            <el-col :span="10">
              <label class="el-col-24" >
                <el-checkbox v-model="element.selected" ></el-checkbox>
                {{element.label}}
              </label>
            </el-col>
            <el-col :span="10" style="display: inline-block">{{element.type}}</el-col>
            <el-col :span="4" >
              <el-link :style="{ visibility: element.isDefault ?'hidden' : 'visible' }" type="danger" link :icon="icons.Delete" title="删除" @click="obj.jsonObj.tableOption.rowBtns.splice(index,1)"></el-link>
              <el-link type="primary" link :icon="icons.Edit" @click="editBtn(element)" title="编辑" style="margin-left:10px"></el-link>
            </el-col>
          </el-row>
        </template>
      </draggable>
      <el-button type="primary" :icon="icons.Plus" style="font-size: 12px" @click="obj.jsonObj.tableOption.rowBtns.push({label:'行按钮'})"> 增加行按钮</el-button>
    </el-form-item>
  </el-card>




  <el-dialog
    v-model="codeRef.show"
    :title="codeRef.title"
    :destroy-on-close="true"
    width="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    draggable
  >
    <MonacoEditor v-model="codeRef.code" language="json" style="height:400px"/>

  </el-dialog>
  <ColumnDialog ref="columnDialogRef" ></ColumnDialog>
  <BtnDialog ref="btnDialogRef" ></BtnDialog>
</template>

<script setup lang="tsx" name="ZGridOption">
import {inject, onMounted, reactive, ref, watch, computed} from "vue";
import _ from 'lodash-es'
import { Props } from "./interface"
import * as icons from "@element-plus/icons-vue";
import draggable from 'vuedraggable';
import http from "@/api";
import MonacoEditor from '@/components/MonacoEditor/index.vue'
import ColumnDialog from './ColumnDialog.vue'
import BtnDialog from './BtnDialog.vue'
import { getColumnListNoPk } from '@/api/dev/column.ts'
import { getViewList} from "@/api/dev/view"
import SelectIcon from "@/components/SelectIcon/index.vue";



let codeRef = ref({
  title: "",
  show: false,
  language: "json",
  code: null
})

const columnDialogRef = ref(null);

const editColumn = (column) => {
  columnDialogRef.value?.openDialog(column);
}

const btnDialogRef = ref(null);


const editBtn = (btn) => {
  btnDialogRef.value?.openDialog(btn,obj.view.mainid);
}


const obj = inject('obj',reactive({
  view: {},
  jsonObj: {
    leftOption:{showLeft:false},
    tableOption:{queryView:null}
  },
}));


// if(!obj.jsonObj.leftOption)obj.jsonObj.leftOption = {}
// if(!obj.jsonObj.tableOption)obj.jsonObj.tableOption = {}
// console.log("obj")
// console.log(JSON.stringify(obj))

const defaultObj ={
  leftOption : {
    showLeft: false,
    showTitle: true,
    showSearch: true,
    leftWidth: 230,
    paramName: "id",
  },
  tableOption: {
    tableApi: '/boot/' + obj.view.hashkey + '/_griddata',
    pagination: true,
    pageable: {
      // 当前页数
      pageNum: 1,
      // 每页显示条数
      pageSize: 10,
      // 总条数
      total: 0,
      pageSizes: [10,20,30],
    },
    showTitle: true,
    icon: 'Grid',
    stripe: true,
    pkType: 'selection',
    columns: [],
    topBtns: [],
    rowBtns: [],
    rowKey: 'id',
  }
}

console.log("obj.jsonObj")
console.log(obj.jsonObj)

obj.jsonObj = _.merge({},defaultObj,obj.jsonObj);

const columnList = ref([]);
const allViewList = ref([]);
const defaultTopBtns = [
  {
    label: '新增',
    name: '__add',
    isDefault: true,
    type: 'primary',
    icon: 'Plus',
    isTop: true,
    action: 'dialog',
  },
  {
    label: '修改',
    name: '__edit',
    isDefault: true,
    type: 'success',
    icon: 'Edit',
    isTop: true,
    action: 'dialog',
  },
  {
    label: '查看',
    name: '__detail',
    isDefault: true,
    type: 'info',
    icon: 'Document',
    isTop: true,
    action: 'dialog',
  },
  {
    label: '删除',
    name: '__delete',
    type: 'danger',
    isDefault: true,
    icon: 'Delete',
    isTop: true,
  },
  {
    label: '刷新',
    name: '__refresh',
    isDefault: true,
    type: 'success',
    icon: 'Refresh',
    isTop: true,
  },
  {
    label: '设置',
    name: '__setting',
    isDefault: true,
    type: 'primary',
    icon: 'Setting',
    isTop: true,
  },
]
const defaultRowBtns = [
  {
    label: '修改',
    name: '__edit',
    isDefault: true,
    type: 'success',
    icon: 'Edit',
    link: true,
  },
  {
    label: '查看',
    name: '__detail',
    isDefault: true,
    type: 'info',
    icon: 'Document',
    link: true,
  },
  {
    label: '删除',
    name: '__delete',
    type: 'danger',
    isDefault: true,
    icon: 'Delete',
    link: true,
  },
]

onMounted( () => {
  getAllColumn();

  getAllView();

  configTopBtns();

  configRowBtns();
})

const getAllColumn = async () => {
  const { data } = await getColumnListNoPk(obj.view.mainid);
  columnList.value = data;
  columnList.value.push({
    name: 'operation',
    showname: '操作',
    isDefault: true,
  })

  columnList.value.forEach(item => {
    if(containColumn(obj.jsonObj.tableOption.columns,item)) return;
    obj.jsonObj.tableOption.columns.push({
      name: item.name,
      prop: item.name,
      label: item.showname,
      dbid: item.id
    })
  })

  var j = obj.jsonObj.tableOption.columns.length-1;

  for(;j>=0;j--) {
    var c = obj.jsonObj.tableOption.columns[j];
    if(containColumn(columnList.value,c))continue;
    obj.jsonObj.tableOption.columns.splice(j,1);
  }
}

const getAllView = async () => {
  const { data } = await getViewList(obj.view.mainid);
  allViewList.value = data;
}


const subViewList = (type) => {
  var list = [];
  allViewList.value.forEach((item) => {
    if(item.type===type) list.push(item);
  })
  return list;
}

const configTopBtns = () => {
  // 1、判断是否默认按钮，是则从默认头部按钮列表中删除
  // 2、将剩余默认头部按钮，添加在末尾

  obj.jsonObj.tableOption.topBtns.forEach((btn) => {
    if(!btn.isTop) btn.isTop = true;
    if(!btn.isDefault) return;
    for(var i=0;i<defaultTopBtns.length;i++) {
      if(btn.name===defaultTopBtns[i].name) defaultTopBtns.splice(i,1);
    }
  })

  defaultTopBtns.forEach((btn) => {
    obj.jsonObj.tableOption.topBtns.push(btn);
  })

}
const configRowBtns = () => {
  // 1、判断是否默认按钮，是则从默认头部按钮列表中删除
  // 2、将剩余默认头部按钮，添加在末尾

  obj.jsonObj.tableOption.rowBtns.forEach((btn) => {
    if(!btn.isTop) btn.isTop = true;
    if(!btn.isDefault) return;
    for(var i=0;i<defaultRowBtns.length;i++) {
      if(btn.name===defaultRowBtns[i].name) defaultRowBtns.splice(i,1);
    }
  })

  defaultRowBtns.forEach((btn) => {
    obj.jsonObj.tableOption.rowBtns.push(btn);
  })

}

const containColumn = (list,c) => {
  for(var i =0;i<list.length;i++) {
    if(list[i].name === c.name) return true;
  }
  return false;
}

const selectAll = (param) => {
  obj.jsonObj.tableOption[param].forEach((item) => {
    item.selected = event?.target?.checked || false;
  })
}

const getRowClass = (index) => {
  return index % 2 === 0 ? 'even-row' : 'odd-row';
}


</script>

<style>
  .block{
    margin: 0 auto;
    width: 92%;
    display: block;
  }

  .even-row {
    background-color: #f2f2f2; /* 偶数行背景色 */
  }
  .odd-row {
    background-color: #ffffff; /* 奇数行背景色 */
  }

</style>
