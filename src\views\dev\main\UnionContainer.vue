<template>
  <div ref="divDom"  :style="{'height': 'calc(100vh - 230px)','background-color': '#ffffff'}">
    <div class="card filter" style="height:50%;width:100%" >
      <ProTable
        ref="proTable0"
        row-key="id"
        :pagination="false"
        :indent="20"
        stripe
        :columns="unionColumns"
        :request-api="getUnionList"
        :request-auto="false"
        :init-param="curMain"
        :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader>
          <el-button type="success" plain :icon="icons.Plus" @click="addUnion">新增关联表</el-button>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button type="primary" link :icon="icons.Edit" @click="editUnion(scope.row,0)">修改</el-button>
          <el-button type="primary" link :icon="icons.Delete" @click="delUnion(scope.row)">删除</el-button>
        </template>
      </ProTable>
    </div>
    <div class="card filter" style="height:50%;width:100%" >
      <ProTable
        ref="proTable1"
        row-key="id"
        :pagination="false"
        :indent="20"
        stripe
        :columns="unionColumns"
        :request-api="getUnionedList"
        :request-auto="false"
        :init-param="curMain"
        :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader>
          <el-button type="success" plain :icon="icons.Plus" @click="addUnioned">新增被关联表</el-button>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button type="primary" link :icon="icons.Edit" @click="editUnion(scope.row,1)">修改</el-button>
          <el-button type="primary" link :icon="icons.Delete" @click="delUnion(scope.row,1)">删除</el-button>
        </template>
      </ProTable>

    </div>

    <UnionDialog ref="unionDialogRef" @save="save"></UnionDialog>
  </div>
</template>
<script lang="ts" setup>
import {watch, ref, reactive, onMounted, inject} from 'vue'
import * as icons from "@element-plus/icons-vue";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";

import ProTable from "@/components/ProTable/index.vue";
import Pagination from "@/components/ProTable/components/Pagination.vue";
import ColSetting from "@/components/ProTable/components/ColSetting.vue";
import TableColumn from "@/components/ProTable/components/TableColumn.vue";

import UnionDialog from "./UnionDialog.vue";
import {getUnionedList, getUnionList, del} from "../../../api/dev/union";

onMounted(()=>{

})

const proTable0 = ref();
const proTable1 = ref();


const save = (type) => {
  if(type==0) {
    proTable0.value?.getTableList();
  }else {
    proTable1.value?.getTableList();
  }
}

const curMain = inject('curMain')

const refresh = () => {
}


const unionDialogRef = ref();

const addUnion = () => {
  unionDialogRef.value?.openDialog({
    dlgProp: {
      title: '新增关联表',
      maintype: curMain?.type,
      type: 0,   //  0 关联表  1 被关联表
    },
    obj: {
      mainid: curMain.id,
      type: ' left join ',
      orderseq: 5,
      uniontype: 0,
    }
  })
}
const addUnioned = (type) => {
  unionDialogRef.value?.openDialog({
    dlgProp: {
      title: '新增被关联表',
      maintype: curMain.type,
      type: 1,   //  0 关联表  1 被关联表
    },
    obj: {
      unionmainid: curMain?.id,
      type: ' left join ',
      orderseq: 5,
      uniontype: 0,
    }
  })
}


const editUnion = (param,type) => {
  if(!type) type = 0;
  let obj = {
    dlgProp: {
      title: type==0?'新增关联表':'新增被关联表',
      maintype: curMain.type,
      type: type,   //  0 关联表  1 被关联表
    },
    obj: param
  }
  unionDialogRef.value?.openDialog(obj);
}


const delUnion = async (param,type) => {
  if(!type) type = 0;
  ElMessageBox.confirm(`是否删除该关联表?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const res = await del(param.id);
    ElMessage({
      type: "success",
      message: `删除关联表成功!`
    });
    save(type);
  }).catch(()=>{});
}




defineExpose({
  refresh
});

// 表格配置项
const unionColumns = reactive<ColumnProps>([
  { prop: "id", label: "ID",width:100 },
  { prop: "name", label: "名称", },
  { prop: "maintable", label: "主表", },
  { prop: "maincolumn", label: "主表字段", },
  { prop: "uniontable", label: "关联表", },
  { prop: "unioncolumn", label: "关联字段", },
  { prop: "unionshowcolumn", label: "显示字段", },
  { prop: "operation", label: "操作", width: 150, fixed: "right" }
]);


</script>

<style scoped lang="scss">
  .objSelected{
    border: 1px solid var(--el-color-primary);
  }
</style>

