<template>
  <div class="login-container flx-center">
    <div class="login-box">
      <SwitchDark class="dark" />
      <div class="login-left">
        <img class="login-left-img" src="@/assets/images/login_left.png" alt="login" />
      </div>
      <div class="login-form">
        <div class="login-logo">
<!--          <img class="login-icon" :src="`/logo_nocode.png`" style="width:auto" alt="" />-->
          <span style="font-size:24px;weight:500" >{{authStore.appinfoGet.showname}}</span>
<!--          <img class="login-icon" :src="logo" alt="" />-->
<!--          <h2 class="logo-text">{{title}}</h2>-->
        </div>
        <div>
          <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
            <el-form-item prop="username">
              <el-input v-model="loginForm.username" placeholder="请输入用户名">
                <template #prefix>
                  <el-icon class="el-input__icon">
                    <user />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" show-password autocomplete="new-password">
                <template #prefix>
                  <el-icon class="el-input__icon">
                    <lock />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <el-row>
            <el-col :span="12"><el-checkbox v-model="remAccount">记住账号</el-checkbox></el-col>
            <el-col :span="12"><el-checkbox v-model="remPwd">记住密码</el-checkbox></el-col>
          </el-row>

          <div class="login-btn">
            <el-button style="width:100%" :icon="UserFilled" round size="large" type="primary" :loading="loading" @click="login(loginFormRef)">
              登录
            </el-button>
          </div>

        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="login">
import SwitchDark from "@/components/SwitchDark/index.vue";
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { HOME_URL } from "@/config";
import { getTimeState } from "@/utils";
import { Login } from "@/api/interface";
import { ElNotification } from "element-plus";
import { loginApi } from "@/api/app/loginApp";
import { useUserStore } from "@/stores/modules/user";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { initDynamicRouter } from "@/routers/dev/modules/dynamicRouter";
import { CircleClose, UserFilled } from "@element-plus/icons-vue";
import type { ElForm } from "element-plus";
import md5 from "md5";
import {useAuthDevStore} from "@/stores/modules/authDev";
// import logo from "@/assets/images/logo.svg?url"

const authStore = useAuthDevStore();

if(!authStore.appinfoGet.showname) authStore.getAppInfo();

console.log("authStore.appinfoGet")
console.log(authStore.appinfoGet)

const remAccount = ref(true);
const remPwd = ref(false)
const router = useRouter();
const userStore = useUserStore();
const tabsStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();

type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const loginRules = reactive({
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }]
});

const loading = ref(false);
const loginForm = reactive<Login.ReqLoginForm>({
  username: "",
  password: ""
});

// login
const login = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    try {
      const pathname = location.pathname;
      const i = pathname.indexOf('/',1)
      let appname;
      if(i===pathname.length-1) {
        appname = pathname.substring(1,i);
      }else if(i<0) {
        appname = pathname.substring(1);
      }


      // 1.执行登录接口
      const { data } = await loginApi({ ...loginForm, password: md5(loginForm.password) }, appname);
      userStore.setTokenUser(data._zreport_user_token);
      userStore.setUserInfo(data.user);
      userStore.setApp(data.app);

      console.log(data);
      // 2.添加动态路由
      await initDynamicRouter();

      // 3.清空 tabs、keepAlive 数据
      tabsStore.setTabs([]);
      keepAliveStore.setKeepAliveName([]);

      // 4.跳转到首页
      router.push(HOME_URL);
      ElNotification({
        title: getTimeState(),
        message: "欢迎登录 无代码编程平台",
        type: "success",
        duration: 3000
      });
    } finally {
      loading.value = false;
    }
  });
};

// resetForm
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

onMounted(() => {
  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e;
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value) return;
      login(loginFormRef.value);
    }
  };
});


</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
