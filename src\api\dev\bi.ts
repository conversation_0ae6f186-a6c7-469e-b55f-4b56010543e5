import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 获取BI对象列表
export const getBilist = () => {
  return http.get(PORT1 + `/../avue/bilist`);
};


// 获得字段列表
export const getColumnlist = (mainid) => {
  return http.get(PORT1 + `/../avue/columnlist?mainid=` + mainid);
};

// 查询BI
export const queryBi = (param) => {
  return http.post(PORT1 + `/../avue/queryBi`, param);
};

// 查询SQL
export const querySql = (param) => {
  return http.post(PORT1 + `/../avue/querySql`, param);
};




