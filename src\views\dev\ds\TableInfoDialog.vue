<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据表详情"
    :destroy-on-close="true"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    draggable
  >
    <el-form
      ref="formRef"
      :model="tableInfo"
      :rules="tableInfoRule"
      status-icon
      inline
      :hide-required-asterisk="false"
      label-width="120px"
      style="padding-top:20px"
    >
      <el-form-item label="表名 :"  prop="tablename">
        <el-input v-model="tableInfo.tablename"></el-input>
      </el-form-item>
      <el-form-item label="表注释 :"  >
        <el-input v-model="tableInfo.tablecomment"></el-input>
      </el-form-item>
      <el-form-item label=""  >
        <el-button type="primary" @click="saveTableInfo(formRef)">保存</el-button>
      </el-form-item>
    </el-form>
    <div style="width:100%;">
      <ProTable
        ref="proTable"
        row-key="dbname"
        :indent="20"
        stripe
        :columns="columns"
        :pagination="false"
        :tool-button="false"
        :request-api="getTableInfo"
        :request-auto="true"
        :init-param="initParam"
        :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
      >
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button type="primary" link :icon="icons.Edit" @click="openEditColumn(scope.row)" >修改</el-button>
          <el-button type="primary" link :icon="icons.Delete" @click="delColumnRow(scope.row)"
                     v-if="initParam.type=='mysql' || initParam.type=='mariadb'">删除</el-button>
        </template>
      </ProTable>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" style="float:left" :icon="icons.CirclePlus" @click="openEditColumn({})"
                   v-if="initParam.type=='mysql' || initParam.type=='mariadb'">新增字段</el-button>
        <el-button type="danger" style="float:left" :icon="icons.Delete" @click="delTableRow" >删除表</el-button>
        <el-button type="primary" @click="createMis">创建MIS</el-button>
        <el-button type="primary" @click="createBI">创建BI</el-button>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>

  </el-dialog>
  <EditColumn ref="editColumnRef" @save="proTable.search()"></EditColumn>
</template>

<script setup lang="ts" name="TableInfoDialog">
import {defineEmits, onMounted, reactive, ref} from "vue";
import {
  ElNotification,
  UploadRequestOptions,
  UploadRawFile,
  FormRules,
  FormInstance,
  ElMessage,
  ElMessageBox
} from "element-plus";
import {Ds, ResPage} from "@/api/interface";
import {saveTable, delColumn, delTable, getTableInfo, makeMIS, makeBI} from "@/api/dev/ds";
import http from "@/api";
import {PORT1} from "@/api/config/servicePort";
import ProTable from "@/components/ProTable/index.vue";
import * as icons from "@element-plus/icons-vue";
import {ProTableInstance} from "@/components/ProTable/interface";

import EditColumn from "@/views/dev/ds/EditColumnDrawer.vue"
import {useRouter} from "vue-router";

onMounted(() => {
});


// ProTable 实例
const proTable = ref<ProTableInstance>();

const formRef = ref()


// dialog状态
const dialogVisible = ref(false);


const initParam = reactive({ dsid: "",tablename: "" });
const tableInfo = reactive({})

let oldTablename ;

// 接收父组件参数
const openDialog = (params) => {
  oldTablename = params.tablename;

  Object.assign(tableInfo,{
    tablename :params.tablename,
    tablecomment :params.tablecomment,
  })
  Object.assign(initParam,{
    dsid : params.dsid,
    tablename :params.tablename,
    type: params.type,
  });


  dialogVisible.value = true;
};


const columns = reactive([
  { type: "index", label: "#", width: 80 },
  { prop: "dbcolumn", label: "字段名称",sortable:true },
  { prop: "dbtype", label: "字段类型",sortable:true },
  { prop: "length", label: "字段长度",sortable:true },
  { prop: "showname", label: "字段描述",sortable:true },
  { prop: "operation", label: "操作", width: 300, fixed: "right" }
]);


const editColumnRef = ref();

const openEditColumn = (row) => {
  const param = {
    dsid : initParam.dsid,
    tablename :initParam.tablename,
    type: initParam.type,
  };
  Object.assign(param,row)
  editColumnRef.value?.openDrawer(param);
}

const delColumnRow = (row) => {
  const name = (row.showname?row.showname:row.dbcolumn)
  ElMessageBox.confirm(`是否删除该字段（` + name + `）吗?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {

    const param = {
      dsid : initParam.dsid,
      tablename :initParam.tablename,
      columnname: row.dbcolumn,
    };
    const res = await delColumn(param);

    ElMessage({
      type: "success",
      message: `删除字段（` + name + `）成功!`
    });
    proTable.value?.getTableList();

  }).catch(()=>{})
}


const delTableRow = () => {
  ElMessageBox.confirm(`是否删除该表（` + oldTablename + `）吗?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {

    const param = {
      dsid : initParam.dsid,
      tablename :oldTablename,
    };
    const res = await delTable(param);

    ElMessage({
      type: "success",
      message: `删除表（` + oldTablename + `）成功!`
    });

    dialogVisible.value = false;

    emit('save')

  }).catch(()=>{})
}




const emit = defineEmits(['save'])

const saveTableInfo = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const param = {
        dsid: initParam.dsid,
        oldTablename: oldTablename,
        tablename: tableInfo.tablename,
        tablecomment: tableInfo.tablecomment,
      }
      const resp = await saveTable(param) ;

      if(resp.code === 200) {
        ElMessage.success(resp.code);
        oldTablename = tableInfo.tablename;
        emit('save')
      }else {
        ElMessage.error(resp.code);
      }
    } catch (error) {
      console.log(error);
    }
  });
}

const tableInfoRule = reactive({
  tablename: [{ required: true, message: "请输入表名!" }],
});

const router = useRouter();

const createMis = async () => {
  const resp = await makeMIS(initParam.dsid,oldTablename);

  if(resp.code === 200) {
    ElMessageBox.confirm("上传成功，是否跳转?", "上传成功", {
      confirmButtonText: "跳转到新创建数据对象",
      cancelButtonText: "取消",
      type: "success"
    }).then(() => {
      router.push("/dev/main/editor/" + resp.data)
    });
  }
}

const createBI = async () => {
  const resp = await makeBI(initParam.dsid,oldTablename);

  if(resp.code === 200) {
    ElMessageBox.confirm("上传成功，是否跳转?", "上传成功", {
      confirmButtonText: "跳转到新创建数据对象",
      cancelButtonText: "取消",
      type: "success"
    }).then(() => {
      router.push("/dev/main/editor/" + resp.data)
    });
  }
}


defineExpose({
  openDialog
});
</script>
