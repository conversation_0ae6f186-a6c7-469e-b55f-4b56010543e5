<template>
  <div class="main-box">
    <div :style="{'width': 'calc(100% - ' + width + 'px)'}">
      <KeepAlive>
        <component
          :is="p.component.name"
          v-bind="p"
        ></component>
      </KeepAlive>
    </div>
    <div class="card filter" :style="{'width': width + 'px'}">
      <p>
        参数配置
        <div style="display: inline;float: right">
          <el-button type="success" :icon="icons.Check" circle title="保存"  />
          <el-button type="primary" :icon="icons.Share" circle title="预览" />
        </div>
      </p>
      <el-form label-width="90px"
               label-position="left"
               size="small">
        <component
          :is="p.component.name+'Option'"
          v-bind="p"
        ></component>
      </el-form>
    </div>

    <ZDialog ref="dlgRef"></ZDialog>

  </div>
</template>

<script setup lang="ts" name="build">
import {reactive, ref} from "vue";
import * as icons from '@element-plus/icons-vue'
import  ZDialog  from '@/components/ZDialog/index.vue'


const dlgRef = ref({});
const props = defineProps({
  id: Number,
  mainid: Number,
  hashkey: String,
  type : String,
  title: String,
  fit: String,
  json: String,
})


const openDlg = (param) => {
  dlgRef.value?.openDlg(param);
}

const width = ref(335);

let prop = {
  leftOption : {
    showLeft: true,            // 显示左边
    title: "标题111" ,
    showTitle: true,         // 显示搜索
    showSearch: true,         // 显示搜索
    leftWidth: 230,
    leftApi: "/dev/datasource/dsList",  // 左边API
    paramName: "dsid",           // 查询参数
  },
  tableOption : {
    tableApi: "/dev/datasource/tablelist",
    columns: [
      { type: "index", label: "#", width: 80 },
      { prop: "tablename", label: "表名",sortable:true, search: { el: "input" } },
      { prop: "tablecommon", label: "表注释",sortable:true,search: { el: "input" } },
      { prop: "operation", label: "操作", width: 300, fixed: "right" }
    ],
    border : false,
    pagination: true,
    toolButton: true,
    pageable: {
      // 当前页数
      pageNum: 1,
      // 每页显示条数
      pageSize: 10,
      // 总条数
      total: 0,
      pageSizes: [10,20,130],
    },
    topBtns : [
      {
        label: "头部按钮1",
        type: "primary",
        plain: true,
        icon: "Grid",
        clickFunction: function(){
          openDlg(1)
        }
      }
    ],
    rowBtns : [
      {
        label: "行按钮",
        type: "primary",
        icon: "CirclePlus",
        clickFunction: function(row){
          console.log(row)
          openDlg({title:'测试',comName:'el-input',width:'100%'})
        }
      }
    ]
  },
  component: {
    name: "ZcodeTable"
  }
}

let p = reactive(prop)

</script>

