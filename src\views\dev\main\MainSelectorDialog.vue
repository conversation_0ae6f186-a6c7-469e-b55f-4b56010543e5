<template>
  <el-dialog
    v-model="dialogVisible"
    title="请选择数据对象"
    :destroy-on-close="true"
    width="80%" top="2%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    draggable
  >
    <div style="width:100%;">
      <ProTable
        ref="proTable"
        row-key="id"
        :indent="20"
        stripe
        :columns="columns"
        :tool-button="false"
        :init-param="initParam"
        :request-api="getMainList"
        @row-dblclick="rowDblClick"
        :request-auto="true"
        :search-col="{ xs: 1, sm: 1, md: 4, lg: 4, xl: 4 }"
      >
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button type="primary" link :icon="icons.Check" @click="selectRow(scope.row)" >选择</el-button>
        </template>
      </ProTable>
    </div>
  </el-dialog>
</template>

<script setup lang="ts" name="MainSelectorDialog">
import {defineEmits, onMounted, reactive, ref} from "vue";
import {
  ElNotification,
  UploadRequestOptions,
  UploadRawFile,
  FormRules,
  FormInstance,
  ElMessage,
  ElMessageBox
} from "element-plus";
import {Ds, ResPage} from "@/api/interface";
import { getMainList} from "@/api/dev/main";
import http from "@/api";
import {PORT1} from "@/api/config/servicePort";
import ProTable from "@/components/ProTable/index.vue";
import {ProTableInstance} from "@/components/ProTable/interface";
import * as icons from "@element-plus/icons-vue";

onMounted(() => {

});


// ProTable 实例
const proTable = ref<ProTableInstance>();

const formRef = ref()


// dialog状态
const dialogVisible = ref(false);

const initParam = ref({
  type: 1
});

// 接收父组件参数
const openDialog = (params) => {
  if(params) initParam.value.type = params;

  dialogVisible.value = true;
};


const columns = reactive([
  { type: "index", label: "#", width: 80 },
  { prop: "name", label: "名称",sortable:true, search: { el: "input" }  },
  { prop: "showname", label: "显示名",sortable:true, search: { el: "input" }  },
  { prop: "dbname", label: "数据源",sortable:true },
  { prop: "tablename", label: "表名",sortable:true , search: { el: "input" }},
  { prop: "operation", label: "操作", width: 200, fixed: "right" }
]);


const emit = defineEmits(['selected'])

const rowDblClick = (row) => {
  selectRow(row);
}


const selectRow = (row) => {
  console.log(row)
  emit('selected',row)
  dialogVisible.value = false;
}


defineExpose({
  openDialog
});
</script>
