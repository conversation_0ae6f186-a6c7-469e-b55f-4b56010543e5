import { ResPage, Ds } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 获取文件列表
export const getFileList = (fileDef) => {
  return http.post(PORT1 + `/../dev/file/list`,fileDef);
};


// 保存文件
export const saveFile = (fileDef) => {
  return http.post(PORT1 + `/../dev/file/save`,fileDef);
};

// 创建目录
export const mkdir = (fileDef) => {
  return http.post(PORT1 + `/../dev/file/mkdir`,fileDef);
};

// 创建空文件
export const createFile = (fileDef) => {
  return http.post(PORT1 + `/../dev/file/createFile`,fileDef);
};

// 改名
export const renameFile = (fileDef) => {
  return http.post(PORT1 + `/../dev/file/rename`,fileDef);
};


// 删除
export const delFileDef = (fileDef) => {
  return http.post(PORT1 + `/../dev/file/del`,fileDef);
};

// 移动文件
export const moveFile = (fileDef) => {
  return http.post(PORT1 + `/../dev/file/move`,fileDef);
};


// 创建文件（带内容）
export const mkfile = (fileDef) => {
  return http.post(PORT1 + `/../dev/file/mkfile`,fileDef);
};

// 上传
export const uploadFile = (fileDef) => {
  return http.post(PORT1 + `/../dev/file/upload`,fileDef);
};



