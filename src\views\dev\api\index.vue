<template>
  <div class="main-box">
    <div class="card filter">
      <h4 class="title sle">API
        <el-button style="float:right;margin-left:5px" circle :icon="icons.Refresh" @click="getApi" title="刷新" />
        <el-button style="float:right" circle :icon="icons.Plus"  @click="addApi" title="新增API" />
      </h4>
      <el-input v-model="filterText" placeholder="输入关键字进行过滤" clearable />
      <el-scrollbar :style="{ height: `calc(100% - 107px)`  }">
        <template v-for="api in apiData">
        <div v-if="showItem(api)"
             :id="api?.id"
             v-on:click="handleApiClick(api)"
             class="apiNode"
             :class="classObject(api?.id)" style="display:flex">
          <span style="display:inline-block;width:40px;margin:5px">
            <div>
              <el-tag effect="dark"  type="primary" >API</el-tag>
            </div>

          </span>
          <span class="apiInfo" >
            <div>{{ api.name }}</div>
            <div>{{ api.keyword }}</div>
          </span>
        </div>
        </template>
      </el-scrollbar>
    </div>

    <div class="table-box" style="background-color:#ffffff">
      <el-form
        ref="formRef"
        :model="currentApi"
        status-icon
        :rules="apiRules"
        label-suffix=":"
        :hide-required-asterisk="false"
        label-width="140" class="card"
        style="width:100%;padding-top:20px;height:100%;margin-left: 5px;"
      >
        <el-row >
          <el-col :span="24">
            <el-form-item label="" label-width="0" >
              <h2 v-if="currentApi.devid==0">查看API（公有API不可编辑）</h2>
              <h2 v-else-if="currentApi.id">编辑API</h2>
              <h2 v-else>新增API</h2>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="名称" prop="name" >
              <el-input v-model="currentApi.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关键词" prop="keyword" >
              <el-input v-model="currentApi.keyword"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" >
            <el-form-item label="数据源" prop="dsid" >
              <el-select v-model="currentApi.dsid" clearable >
                <el-option v-for="(t) in dsList" :key="t?.id" :value="t?.id" :label="t.type + '-' + t.name + '  :  ' + t.ip"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否登录">
              <el-switch
                v-model="currentApi.islogin"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="SQL语句"  prop="sqls" >
              <MonacoEditor v-model="currentApi.sqls" language="sql" style="height:180px"/>
              <el-tooltip
                class="box-item"
                effect="dark"
                placement="right-start"
              >
                <template #content> 以@param@来替换输入参数<br />多个sql以分号“;”隔开<br />当类型为数据操作时，最后一个sql必须是返回数值型的查询语句</template>
                <el-icon style="margin-left:20px"><QuestionFilled /></el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参数" >
              <el-input v-model="currentApi.params"></el-input>
              <el-tooltip
                class="box-item"
                effect="dark"
                placement="right-start"
              >
                <template #content>  API参数可以替换掉API数据中的#号，或者@param@ <br />多个参数以分号“;”隔开
                </template>
                <el-icon style="margin-left:20px"><QuestionFilled /></el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" >
              <el-input v-model="currentApi.memo"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="类型" >
              <el-radio-group v-model="currentApi.type">
                <el-radio value="data">数据查询（返回数组）
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    placement="right-start"
                  >
                    <template #content>  返回数组的数组: [[value,value...],[]...]
                    </template>
                    <el-icon style="margin-left:20px"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </el-radio>
                <el-radio value="object">数据查询（返回对象）
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    placement="right-start"
                  >
                    <template #content>  返回对象的数组: [{key:value,key:value...},{}...}
                    </template>
                    <el-icon style="margin-left:20px"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </el-radio>
                <el-radio value="op">数据操作/数据判定
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    placement="right-start"
                  >
                    <template #content>  返回操作结果的json，可用于提示对话框
                    </template>
                    <el-icon style="margin-left:20px"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="currentApi.type=='op'">
            <el-col :span="24">
              <el-form-item label="数据判定" >
                <el-select v-model="currentApi.votetype" style="width: 40%">
                  <el-option v-for="(item) in votetypes" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
                <el-input v-model="currentApi.votevalue"  style="width: 40%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="正确信息" >
                <el-input v-model="currentApi.okmsg"  ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="错误信息" >
                <el-input v-model="currentApi.errmsg"  ></el-input>
              </el-form-item>
            </el-col>
          </template>

          <el-col :span="12">
          </el-col>
          <el-col :span="12">
            <el-form-item label=""  >
              <el-button type="success" v-if="currentApi.id" @click="cloneEnum">克隆</el-button>
              <el-button type="primary" @click="saveEnumAction">保存</el-button>
              <el-button type="danger" v-if="currentApi.id" @click="delEnum">删除</el-button>
            </el-form-item>
          </el-col>
          <el-card style="width:100%"  v-if="currentApi.id">
            <template #header>API测试</template>
            <el-col :span="24">
              <el-form-item label="API接口地址" >
                <el-link type="primary"  >/api/{{ userStore.devInfo.account }}/{{ currentApi.keyword }}</el-link>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="测试参数" >
                <el-input  style="width:50%" v-model="currentApi.testParams"></el-input>
                <el-button type="primary" @click="handleTest">提交测试</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="测试结果">
                <MonacoEditor v-model="currentApi.testResp" language="json" style="height:200px"/>
              </el-form-item>
            </el-col>

          </el-card>


        </el-row>
      </el-form>

    </div>
  </div>
</template>

<script setup lang="tsx" name="apiIndex">
import {onMounted, reactive, ref, watch} from "vue";
import {ElMessage, ElMessageBox, ElNotification, FormRules, FormInstance} from "element-plus";
import * as icons from "@element-plus/icons-vue";
import {getApiList, save, del, test} from "@/api/dev/api";
import {getDsList} from "@/api/dev/ds";
import UploadImg from "@/components/Upload/Img.vue";
import {Ds} from "@/api/interface";
import ProTable from "@/components/ProTable/index.vue";
import MonacoEditor from '@/components/MonacoEditor/index.vue'
import { useUserStore } from "@/stores/modules/user";


const cacheList = [0,-1,3600,86400];

const votetypes = [
  {
    label: "等于（=）",
    value: "="
  },
  {
    label: "大于（>）",
    value: ">"
  },
  {
    label: "大于等于（>=）",
    value: ">="
  },
  {
    label: "小于（<）",
    value: "<"
  },
  {
    label: "小于等于（<=）",
    value: "<="
  },
]

const dsList = ref([]);


const userStore = useUserStore();

onMounted(async () => {
  getApi();
  const { data } = await getDsList();
  dsList.value = data;

});


const apiData = ref<{ [id: string]: any }[]>([]);


const filterText = ref("");

const showItem = (api) => {
  if(filterText.value == "") return true;

  if(api.name!=null && api.name.indexOf(filterText.value)>=0) return true;
  if(api.showname!=null && api.showname.indexOf(filterText.value)>=0) return true;
  return false;
}

const getApi = async (id) => {
  const { data } = await getApiList();

  apiData.value = data;

  if(!data || data.length==0) return;

  if(id) {
    for(var i=0;i<data.length;i++) {
      if(data[i]?.id==id) {
        handleApiClick(apiData.value[i]);
      }
    }
  }else {
    handleApiClick(apiData.value[0]);
  }
};



const currentApi = ref({});

const handleApiClick = (api) => {
  Object.assign(currentApi.value,api);
  currentApi.value.testResp = "";
}

const classObject = (id) => {
  return currentApi.value?.id == id  ? "apiSelected" : "apiNoselected";
}

const delEnum = async () => {
  ElMessageBox.confirm(`是否删除该API?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true
  }).then(async () => {
    const res = await del(currentApi.value.id);
    ElMessage({
      type: "success",
      message: `删除API成功!`
    });
    getApi()
  }).catch(()=>{});
}

const apiTabName = ref("base");
const defineTabName = ref("user");

const addApi = () => {
  currentApi.value = {
    showname : '',
    type : 'data',
  };
}


const apiRules = reactive({
  // type: [{ required: true,trigger: 'change', message: "请选择一个数据库类型" }],
  name: [{ required: true, message: "请输入API名称" }],
  keyword: [{ required: true, message: "请输入关键词" }],
  dsid: [{ required: true, message: "请选择数据源" }],
  sqls: [{ required: true, message: "请输入SQL语句" }],
});



const formRef = ref<FormInstance>()


const saveEnumAction = async () => {

  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const resp = await save(currentApi.value);

      if(resp.code==200) {
        ElMessage.success({ message: resp.msg });

        getApi(resp.data)

      }else {
        ElMessage.error({ message: resp.msg });
      }


    } catch (error) {
      console.log(error);
    }
  });

}

const cloneEnum = () => {
  currentApi.value.id = null;
  currentApi.value.devid = null;
}

const handleTest = async () => {
  const resp = await test(userStore.devInfo.account,currentApi.value.keyword,currentApi.value.testParams);
  currentApi.value.testResp = JSON.stringify(resp);
}


</script>
<style scoped lang="scss">
  .apiSelected{
    border: 1px solid var(--el-color-primary);
  }
  .apiNoselected{
    border: 1px solid rgba(0,0,0,0);
  }
  .apiNode{
    height: 50px;
    font-size: 14px;
    margin: 8px 2px;
    overflow: hidden; /* 确保文本超出容器时会被裁剪 */
    white-space: nowrap; /* 确保文本不会换行 */
    text-overflow: ellipsis; /* 在文本末尾显示省略号 */
    border-bottom: 1px solid var(--el-color-primary-light-3);
  }
  .apiInfo {
    width: 110px;height:56px; display: inline-block;margin-left: 5px;
    overflow: hidden; /* 确保文本超出容器时会被裁剪 */
    white-space: nowrap; /* 确保文本不会换行 */
    text-overflow: ellipsis; /* 在文本末尾显示省略号 */
  }
</style>

