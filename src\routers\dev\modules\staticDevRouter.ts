import { RouteRecordRaw } from "vue-router";
import { HOME_URL, LOGIN_URL } from "@/config";

/**
 * staticDevRouter (静态路由)
 */
export const staticDevRouter: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: HOME_URL
  },
  {
    path: LOGIN_URL,
    name: "login",
    component: () => import("@/views/dev/login/index.vue"),
    meta: {
      title: "登录"
    }
  },
  {
    path: "/dev/build/:key",
    name: "build",
    component: () => import("@/views/dev/build/index.vue"),
    meta: {
      title: "视图编辑器"
    }
  },
  {
    path: "/dev/appmenu/:appid",
    name: "appmenu",
    component: () => import("@/views/dev/appmenu/index.vue"),
    meta: {
      title: "应用菜单编辑器"
    }
  },
  {
    path: "/view/:key",
    name: "view",
    component: () => import("@/views/front/Preview.vue"),
    meta: {
      title: "视图预览"
    }
  },
  {
    path: "/layout",
    name: "layout",
    // component: () => import("@/layouts/index.vue"),
    component: () => import("@/layouts/indexAsync.vue"),
    redirect: HOME_URL,
    children: []
  },
  {
    path: "/FormDesigner",
    name: "FormDesigner",
    component: () => import("@/views/dev/FormDesigner.vue"),
    meta: {
      title: "视图预览"
    }
  }
];

/**
 * errorRouter (错误页面路由)
 */
export const errorRouter = [
  {
    path: "/403",
    name: "403",
    component: () => import("@/components/ErrorMessage/403.vue"),
    meta: {
      title: "403页面"
    }
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/components/ErrorMessage/404.vue"),
    meta: {
      title: "404页面"
    }
  },
  {
    path: "/500",
    name: "500",
    component: () => import("@/components/ErrorMessage/500.vue"),
    meta: {
      title: "500页面"
    }
  },
  // Resolve refresh page, route warnings
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/components/ErrorMessage/404.vue")
  }
];
