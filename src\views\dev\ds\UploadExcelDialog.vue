<template>
  <el-dialog v-model="dialogVisible" title="上传Excel" size="600px">
    <el-form
      ref="formRef"
      :model="obj"
      status-icon
      :hide-required-asterisk="false"
      label-width="120px"
    >
      <el-form-item label="操作 :" prop="makeType" >
        <el-radio-group v-model="obj.makeType">
          <el-radio-button label="上传Excel后自动建表" value="" style="display:block"></el-radio-button>
          <el-radio-button label="上传Excel后自动创建 MIS" value="MIS" style="display:block"></el-radio-button>
          <el-radio-button label="上传Excel后自动创建 BI" value="BI" style="display:block"></el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" >
        <el-upload
          class="upload-demo"
          :action="uploadUrl"
          :auto-upload="true"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          :data="obj"
          :headers="headObj"
          :with-credentials="true"
          :show-file-list="false"
          :on-success="uploadSuccess"
        >
          <el-button type="primary">上传Excel</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts" name="UploadExcelDialog">
import {defineEmits, onMounted, ref} from "vue";
import type { UploadProps } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import {useUserStore} from "@/stores/modules/user";
import {useRouter} from "vue-router";

const uploadUrl = import.meta.env.VITE_API_URL + "/dev/file/upload"

const dialogVisible = ref<boolean>(false);

const obj = ref({
  makeType: ""
});


const openDialog = (param) => {
  obj.value.dsid = param;
  dialogVisible.value = true;
};

const emit = defineEmits(['save'])

const headObj = ref();

onMounted(() => {
  const userStore = useUserStore();
  headObj.value = new Headers({
    _zreport_dev_token : userStore._zreport_dev_token
  });

})


const formRef = ref();

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type !== 'application/vnd.ms-excel') {
    ElMessage.error('请上传Excel文件!')
    return false
  }
  // else if (rawFile.size / 1024 / 1024 > 20) {
  //   ElMessage.error('文件大小不要超过 20MB!')
  //   return false
  // }
  return true
}

const router = useRouter();

const uploadSuccess =  (
  resp,
  file
) => {
  emit('save');

  if(resp.data) {
    ElMessageBox.confirm("上传成功，是否跳转?", "上传成功", {
      confirmButtonText: "跳转到新创建数据对象",
      cancelButtonText: "取消",
      type: "success"
    }).then(() => {
      router.push("/view/" + resp.data)
    }).catch(() => {
    });
  }else {
    ElMessage.success("上传成功");
  }

  dialogVisible.value = false;
}



defineExpose({
  openDialog
});
</script>

