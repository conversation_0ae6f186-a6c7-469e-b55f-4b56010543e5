<template>
  <el-dialog
    v-model="dialogVisible"
    title="通过 ChatGPT 自动创建表 / 创建系统"
    :destroy-on-close="true"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    draggable
  >
    <el-form
      ref="formRef"
      :model="dsForm"
      status-icon
      inline
      :hide-required-asterisk="false"
      label-width="120px"
      style="margin-top: 20px"
    >
      <el-form-item label=""  >
        <el-input v-model="dsForm.question" style="width:800px"></el-input>
      </el-form-item>
      <el-form-item label=""  >
        <el-button type="primary" @click="aiAskFun">提问到 ChatGPT</el-button>
      </el-form-item>

      <MonacoEditor ref="monacoEditorRef" v-model="dsForm.sql" language="sql" style="height:400px"/>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="aiCreateTableFun">创建表</el-button>
        <el-button type="primary" @click="aiCreateAppFun">创建应用</el-button>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="ChatGPTDialog">
import {onUnmounted, reactive, ref,defineExpose} from "vue";
import {ElMessage, FormInstance} from "element-plus";
import {aiAsk, aiCreateTable, aiCreateApp} from "@/api/dev/ai";
import MonacoEditor from '@/components/MonacoEditor/index.vue'


let dsForm = reactive({})

const monacoEditorRef = ref<InstanceType<typeof MonacoEditor>>()
// onUnmounted(() => {
//   onDispose()
// })

const formRef = ref<FormInstance>()


// dialog状态
const dialogVisible = ref(false);

// 接收父组件参数
const openDialog = (dsid) => {
  dsForm.dsid = dsid;
  dialogVisible.value = true;
};

const aiAskFun = async () => {
  // const { data } = await aiCreateTable(question);
  if(!dsForm.question) return ;
  dsForm.sql = await aiAsk(dsForm.question);
}

const aiCreateTableFun = async () => {
  if(!dsForm.dsid || !dsForm.sql) return;

  const resp =  await  aiCreateTable(dsForm);
  if(resp.code === 200) {
    ElMessage.success(resp.msg)
  }else {
    ElMessage.error(resp.msg)
  }
}

const aiCreateAppFun = async () => {
  if(!dsForm.dsid || !dsForm.sql) return;
  const resp =  await  aiCreateApp(dsForm);
  if(resp.code === 200) {
    ElMessage.success(resp.msg)
  }else {
    ElMessage.error(resp.msg)
  }
}

defineExpose({
  openDialog
});
</script>
