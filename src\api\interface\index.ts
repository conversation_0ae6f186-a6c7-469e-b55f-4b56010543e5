// 请求响应参数（不包含data）
export interface Result {
  code: string;
  msg: string;
}

// 请求响应参数（包含data）
export interface ResultData<T = any> extends Result {
  data: T;
}

// 分页响应参数
export interface ResPage<T> {
  list: T[];
  pageNum: number;
  pageSize: number;
  total: number;
}

// 分页请求参数
export interface ReqPage {
  pageNum: number;
  pageSize: number;
}

// 文件上传模块
export namespace Upload {
  export interface ResFileUrl {
    fileUrl: string;
  }
}

// 登录模块
export namespace Login {
  export interface ReqLoginForm {
    username: string;
    password: string;
    valid: string;
  }
  export interface ResLogin {
    // access_token: string;
    dev: object;
    _zreport_dev_token: string;
    _zreport_user_token: string;
  }
  export interface ResAuthButtons {
    [key: string]: string[];
  }
}
export namespace Ds {
  export interface ResDsListParams extends ReqPage {
    id: number;
    name: string;
    devid: number;
    type: string;
    ip: string;
    port: string;
    username: string;
    password: string;
  }

  export interface ReqDsTableListParams extends ReqPage {
    tablename: string;
    tablecommon: string;
  }

  export interface ResDsTableListParams extends ReqPage {
    tablename: string;
    tablecommon: string;
  }

}

// 用户管理模块
export namespace User {
  export interface ReqUserParams extends ReqPage {
    username: string;
    gender: number;
    idCard: string;
    email: string;
    address: string;
    createTime: string[];
    status: number;
  }
  export interface ResUserList {
    id: string;
    username: string;
    gender: number;
    user: { detail: { age: number } };
    idCard: string;
    email: string;
    address: string;
    createTime: string;
    status: number;
    avatar: string;
    photo: any[];
    children?: ResUserList[];
  }
  export interface ResStatus {
    userLabel: string;
    userValue: number;
  }
  export interface ResGender {
    genderLabel: string;
    genderValue: number;
  }
  export interface ResDepartment {
    id: string;
    name: string;
    children?: ResDepartment[];
  }
  export interface ResRole {
    id: string;
    name: string;
    children?: ResDepartment[];
  }
}
