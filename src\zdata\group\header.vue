<template>
  <div class="head_actions">
    <div class="head_btn"
         :class="{'head_btn--active':contain.menuShow}"
         @click="handleFlag('menuShow')">
      <el-tooltip effect="dark"
                  content="图层"
                  placement="top">
        <i class="el-icon-s-operation"></i>
      </el-tooltip>
    </div>
    <div class="head_btn"
         :class="{'head_btn--active':contain.paramsShow}"
         @click="handleFlag('paramsShow')">
      <el-tooltip effect="dark"
                  content="操作"
                  placement="top">
        <i class="el-icon-setting"></i>
      </el-tooltip>
    </div>
    <div style="border-left: 1px solid rgb(204, 204, 204);width: 5px;height: 24px;margin-left: 7px;"></div>
    <div class="head_btn"
         @click="handleBuild">
      <el-tooltip effect="dark"
                  content="保存"
                  placement="top">
        <i class="iconfont icon-build"></i>
      </el-tooltip>
    </div>
    <div class="head_btn"
         @click="handleShare">
      <el-tooltip effect="dark"
                  content="打开页面"
                  placement="top">
        <i class="el-icon-share"></i>
      </el-tooltip>
    </div>
    <div class="head_btn"
         @click="handleImg">
      <el-tooltip effect="dark"
                  content="导出图片"
                  placement="top">
        <i class="el-icon-camera"></i>
      </el-tooltip>
    </div>
    <div class="head_btn"
         @click="handleView"
         v-show="contain.menuFlag">
      <el-tooltip effect="dark"
                  content="预览"
                  placement="top">
        <i class="iconfont icon-view"></i>
      </el-tooltip>
    </div>
    <div class="head_btn"
         @click="handleReset"
         v-show="!contain.menuFlag">
      <el-tooltip effect="dark"
                  content="还原"
                  placement="top">
        <i class="iconfont icon-reset"></i>
      </el-tooltip>
    </div>
    <div class="head_btn"
         @click="$refs.result.show=true">
      <el-tooltip effect="dark"
                  content="导入导出"
                  placement="top">
        <i class="el-icon-download"></i>
      </el-tooltip>
    </div>
    <div class="head_btn"
         :disabled='!contain.canUndo'
         @click="contain.editorUndo">
      <el-tooltip effect="dark"
                  content="撤销"
                  placement="top">
        <i class="nav__icon el-icon-arrow-left"></i>
      </el-tooltip>
    </div>
    <div class="head_btn"
         :disabled='!contain.canRedo'
         @click="contain.editorRedo">
      <el-tooltip effect="dark"
                  content="重做"
                  placement="top">
        <i class="nav__icon el-icon-arrow-right"></i>
      </el-tooltip>
    </div>
    <!-- </div> -->
    <result ref="result"></result>
    <share ref="share"></share>
  </div>

</template>
<script setup lang="ts">
import { url } from '../config';
import result from './result';
import share from './share'
import { updateComponent } from '@/api/visual'

</script>


<style scoped lang="scss">
.head {
  position: relative;
  height: 41px;
  padding-right: 8px;
  display: flex;
  z-index: 100;
  align-items: center;
  user-select: none;
  color: var(--datav-gui-font-color-base);
  border-bottom: var(--datav-border-dark);
  background: #1d1e1f;
  &_actions {
    position: absolute;
    top: 0;
    right: 8px;
    width: 300px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 40px;
  }
  &_left {
    left: 8px;
    justify-content: flex-start;
  }
  &_btn {
    margin-left: 4px;
    width: 40px;
    height: 24px;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
    background: #303640;
    transition: 0.2s;
    i {
      color: #fff;
    }
    &--active {
      background-color: #2681ff;
    }
  }
  &_info {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    text-align: center;
    cursor: default;
    font-size: 14px;
    max-width: 500px;
    font-weight: bold;
    color: #fff;
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    i {
      margin-right: 8px;
      font-size: 20px;
    }
  }
}

.head_btn {
  box-shadow: inset 0 0 0 1px rgb(255 235 235 / 10%), 0 0 0 1px #181818;
}

</style>
