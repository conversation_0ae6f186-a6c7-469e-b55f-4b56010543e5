<template>
  <el-dialog v-model="dialogVisible" title="编辑按钮" width="80%" draggable>
    <el-form
      ref="btnFormRef"
      label-suffix=":"
      label-position="right"
      label-width="150"
      :rules="btnFormRules"
      :model="obj"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="按钮标识" prop="name">
            <el-text type="success" v-if="obj.isDefault">{{obj.name}}</el-text>
            <el-input class="mx-1" v-model="obj.name" clearable v-else />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="按钮文本" prop="label">
            <el-input class="mx-1" v-model="obj.label" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="obj.type" clearable >
              <el-option label="primary" value="primary"></el-option>
              <el-option label="success" value="success"></el-option>
              <el-option label="warning" value="warning"></el-option>
              <el-option label="danger" value="danger"></el-option>
              <el-option label="info" value="info"></el-option>
              <el-option label="text" value="text"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="朴素按钮" prop="plain">
            <el-switch v-model="obj.plain"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="文字按钮" prop="text">
            <el-switch v-model="obj.text"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="显示背景颜色" prop="bg">
            <el-switch v-model="obj.bg"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="链接按钮" prop="link">
            <el-switch v-model="obj.link"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="圆角按钮" prop="round">
            <el-switch v-model="obj.round"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="圆形按钮" prop="circle">
            <el-switch v-model="obj.circle"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="暗黑模式" prop="dark">
            <el-switch v-model="obj.dark"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="背景颜色" prop="color">
            <el-color-picker v-model="obj.color" show-alpha />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="文字颜色" prop="txtColor">
            <el-color-picker v-model="obj.txtColor" show-alpha />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="大小" prop="size">
            <el-radio-group v-model="obj.size" >
              <el-radio-button key="large" value="large" label="large"></el-radio-button>
              <el-radio-button key="default" value="default" label="default"></el-radio-button>
              <el-radio-button key="small" value="small" label="small"></el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="原生类型" prop="nativeType">
            <el-select v-model="obj.nativeType" clearable>
              <el-option label="button" value="button"></el-option>
              <el-option label="submit" value="submit"></el-option>
              <el-option label="reset" value="reset"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图标" prop="icon">
            <SelectIcon v-model="obj.icon" clearable ></SelectIcon>
          </el-form-item>
        </el-col>
        <template v-if="!['__delete','__setting','__refresh'].includes(obj.name)">
          <el-col :span="12">
            <el-form-item label="操作行为" prop="action">
              <el-select v-model="obj.action" >
                <el-option value="dialog" label="弹窗"></el-option>
                <el-option value="drawer" label="抽屉"></el-option>
                <el-option value="label" label="标签页打开"></el-option>
                <el-option value="window" label="新窗口打开"></el-option>
                <el-option value="self" label="当前页面替换"></el-option>
                <el-option value="api" label="API接口调用"></el-option>
                <el-option value="js" label="JS执行"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作目标" prop="target">
              <el-select v-model="obj.target" v-if="obj.name.indexOf('__')==0">
                <el-option v-for="(item) in computerView(obj.name)" :value="item.hashkey" :label="item.title"></el-option>
              </el-select>
              <el-input v-model="obj.target" v-else></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="obj.action=='dialog' || obj.action == 'draw'">
            <el-form-item label="目标宽度" prop="targetSize">
              <el-input v-model="obj.targetSize"></el-input>
            </el-form-item>
          </el-col>
        </template>

        <el-col :span="12" v-if="obj.isTop && !obj.isDefault">
          <el-form-item label="选择数据" prop="selectMode">
            <el-radio-group v-model="obj.selectMode" >
              <el-radio-button :key="0" :value="0" label="不选"></el-radio-button>
              <el-radio-button :key="1" :value="1" label="单选"></el-radio-button>
              <el-radio-button :key="2" :value="2" label="多选"></el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="TopBtnDialog">
import {ref, reactive, withDefaults} from "vue";
import {ElMessage, FormInstance, FormRules} from "element-plus";
import SelectIcon from "@/components/SelectIcon/index.vue";
import { getViewList } from "@/api/dev/view";



const btnFormRef = ref<FormInstance>()

const btnFormRules = reactive<FormRules>({
  // type: [{ required: true,trigger: 'change', message: "请选择一个数据库类型" }],
  name: [{ required: true, message: "请输入字段标识" }],
});


const dialogVisible = ref(false);

const obj = ref({});

const mainid = ref();
let btn ;

const openDialog = (btnInfo,mid) => {
  btn = btnInfo;
  obj.value = {};
  Object.assign(obj.value,btnInfo)

  mainid.value = mid;
  dialogVisible.value = true;
};

defineExpose({ openDialog });

const handleSubmit = () => {
  btnFormRef.value!.validate(async valid => {
    if (!valid) return;

    Object.assign(btn,obj.value)

    dialogVisible.value = false;
  })

}

const viewList = ref([])

const initViewList = async () => {

  const { data } = await getViewList(mainid.value);

  viewList.value = data;
}

const computerView = (type) => {
  if(type.indexOf('__')!=0) return;
  if(viewList.value.length==0) initViewList();

  let list = [];

  const stype = type.substr(2);

  console.log(viewList.value)
  console.log(stype)

  viewList.value.forEach( (view) => {
    if(view.type===stype) list.push(view);
  })
  console.log(list)

  return list;
}


</script>
<style >
  /*.el-form-item__label{*/
  /*  text-align: right;*/
  /*}*/
  .el-form--label-left .el-form-item__label {
    text-align: left !important;
    /*justify-content: flex-end !important; */
  }
</style>
